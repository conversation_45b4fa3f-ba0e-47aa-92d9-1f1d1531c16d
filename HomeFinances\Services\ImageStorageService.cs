﻿using Azure.Storage.Blobs;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace HomeFinances.Services
{
    public class ImageStorageService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly ILogger<ImageStorageService> _logger;
        private const string ContainerName = "receipt-images";

        public ImageStorageService(BlobServiceClient blobServiceClient, ILogger<ImageStorageService> logger)
        {
            _blobServiceClient = blobServiceClient;
            _logger = logger;
        }

        public async Task<string> StoreImageAsync(string imageUrl, string prompt)
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient("generated-images");
            await containerClient.CreateIfNotExistsAsync();
            var blobClient = containerClient.GetBlobClient($"{Guid.NewGuid()}.png");

            using var httpClient = new HttpClient();
            using var response = await httpClient.GetAsync(imageUrl);
            response.EnsureSuccessStatusCode();

            await using var stream = await response.Content.ReadAsStreamAsync();
            await blobClient.UploadAsync(stream, true);

            _logger.LogInformation("Image stored successfully with URL: {ImageUrl}", blobClient.Uri.ToString());
            return blobClient.Uri.ToString();
        }

        public async Task<string> UploadImageAsync(string imagePath)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);
                await containerClient.CreateIfNotExistsAsync();

                var fileName = Path.GetFileName(imagePath);
                var blobClient = containerClient.GetBlobClient(fileName);

                using var fileStream = File.OpenRead(imagePath);
                await blobClient.UploadAsync(fileStream, true);

                return blobClient.Uri.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading image to blob storage");
                throw;
            }
        }
    }
}