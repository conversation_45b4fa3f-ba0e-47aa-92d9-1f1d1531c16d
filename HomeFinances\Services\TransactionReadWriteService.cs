﻿using System.Globalization;
using CsvHelper;
using CsvHelper.Configuration;
using HomeFinances.Models;
using HomeFinances.Models.Comparers;
using HomeFinances.Models.Mapping;
using HomeFinances.Models.Mapping.HomeFinances.Models.Mapping;
using HomeFinances.Repositories;

namespace HomeFinances.Services;

public class TransactionReadWriteService
{
    private readonly IBankTransactionRepository _bankTransactionRepository;
    private readonly CategoryMappingService _categoryMappingService;
    private decimal _currentBalance;

    public TransactionReadWriteService(CategoryMappingService categoryMappingService
        , IBankTransactionRepository bankTransactionRepository)
    {
        _categoryMappingService = categoryMappingService;
        _bankTransactionRepository = bankTransactionRepository;
    }

    public async Task<decimal> GetBeeHiveCurrentBalanceAsync()
    {
        if (_currentBalance == 0) await ReadBeehiveTransactions();
        return _currentBalance;
    }

    private void SetBeeHiveCurrentBalance(decimal value)
    {
        if (_currentBalance != value)
        {
            Console.WriteLine(value: $"Value changing from {_currentBalance} to {value}");
            _currentBalance = value;
        }
    }

    public async Task<List<BankTransaction>> ReadBankTransactionsAsync(string filePath)
    {
        try
        {
            var transactions = await _bankTransactionRepository.ReadTransactionsAsync(filePath: filePath);
            return transactions.Value.ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine(value: $"Failed to read bank transactions: {ex.Message}");
            return new List<BankTransaction>();
        }
    }

    private async Task<IEnumerable<BeeHiveTransaction>> ReadBeehiveTransactions()
    {
        return await ReadBeehiveTransactionsFromFolder(
            csvDirectoryPath: @"Data/Uploads/BeehiveTransactions");
    }

    public async Task<IEnumerable<BeeHiveTransaction>> ReadBeehiveTransactionsFromFolder(string csvDirectoryPath)
    {
        var uniqueTransactions = new HashSet<BeeHiveTransaction>(comparer: new BeeHiveTransactionComparer());
        if (!Directory.Exists(path: csvDirectoryPath))
        {
            Console.WriteLine(value: $"Directory not found: {csvDirectoryPath}");
            return uniqueTransactions;
        }

        var csvFiles = Directory.GetFiles(path: csvDirectoryPath
                                          , searchPattern: "*.csv");
        foreach (var file in csvFiles)
        {
            var transactions = await ReadBeehiveTransactionsFromCsvAsync(filePath: file);
            foreach (var transaction in transactions) uniqueTransactions.Add(item: transaction);
        }

        // Order by TransactionDate and then numerically parse the SequenceNumber for proper ordering
        var sortedTransactions = uniqueTransactions
           .OrderBy(keySelector: t => t.TransactionDate)
           .ThenBy(keySelector: t => ParseSequenceNumber(sequenceNumber: t.SequenceNumber))
           .Reverse()
           .ToList();

        SetBeeHiveCurrentBalance(value: sortedTransactions.FirstOrDefault()?.Balance ?? 0M);
        return sortedTransactions;
    }

    private decimal ParseSequenceNumber(string sequenceNumber)
    {
        // Normalizing and parsing the number
        if (sequenceNumber.StartsWith(value: "+")) sequenceNumber = sequenceNumber.Substring(startIndex: 1);

        if (decimal.TryParse(s: sequenceNumber
                             , style: NumberStyles.Any
                             , provider: CultureInfo.InvariantCulture
                             , result: out var numericValue)) return numericValue;

        throw new FormatException(message: "Sequence number is not in a correct format: " + sequenceNumber);
    }

    private async Task<List<BeeHiveTransaction>> ReadBeehiveTransactionsFromCsvAsync(string filePath)
    {
        var records = new List<BeeHiveTransaction>();
        if (!File.Exists(path: filePath))
        {
            Console.WriteLine(value: $"File not found: {filePath}");
            return records;
        }

        try
        {
            // Use a memory stream to read the file content
            using (var fileStream = new FileStream(path: filePath
                                                   , mode: FileMode.Open
                                                   , access: FileAccess.Read))
            {
                using (var memoryStream = new MemoryStream())
                {
                    await fileStream.CopyToAsync(destination: memoryStream);
                    memoryStream.Position = 0;

                    using (var reader = new StreamReader(stream: memoryStream))
                    {
                        using (var csv = new CsvReader(reader: reader
                                                       , configuration: new CsvConfiguration(
                                                                            cultureInfo: CultureInfo.InvariantCulture)
                                                       { HasHeaderRecord = true }))
                        {
                            csv.Context.RegisterClassMap<BeeHiveTransactionMap>();
                            records.AddRange(collection: csv.GetRecords<BeeHiveTransaction>());
                        }
                    }
                }
            }

            foreach (var record in records)
                record.Taxonomy = await _categoryMappingService.GetCategoryAndSubCategory(description: record.Description);
        }
        catch (Exception ex) when (ex is CsvHelperException || ex is IOException)
        {
            Console.WriteLine(value: $"Failed to read or parse CSV: {ex.Message}");
        }

        return records;
    }

    public async Task<IEnumerable<BankTransaction>> MergeAndSaveBeehiveTransactions(
        IEnumerable<BeeHiveTransaction> beehiveTransactions
        , string bankTransactionsFilePath)
    {
        var bankTransactions = await _bankTransactionRepository.ReadTransactionsAsync(filePath: bankTransactionsFilePath);
        var newBankTransactions = beehiveTransactions.Select(selector: bht => new BankTransaction
        {
            Account = bht.Account
                                                                                  ,
            Description = bht.Description
                                                                                  ,
            TransactionAmount = bht.TransactionAmount
                                                                                  ,
            TransactionDate = bht.TransactionDate
                                                                                  ,
            Taxonomy = bht.Taxonomy
                                                                                  ,
            BankCode
                                                                                      = $"{bht.Suffix}-{bht.SequenceNumber}"
        });

        var updatedTransactions = bankTransactions.Value.Concat(second: newBankTransactions).ToList();
        var uniqueTransactions
            = updatedTransactions.GroupBy(keySelector: t => t.BankCode).Select(selector: g => g.First()).ToList();

        // Write back using the repository
        await _bankTransactionRepository.WriteTransactionsAsync(transactions: uniqueTransactions
                                                                , filePath: bankTransactionsFilePath);

        return uniqueTransactions;
    }

    private void WriteBankTransactionsToCsv(List<BankTransaction> transactions
        , string filePath)
    {
        using (var writer = new StreamWriter(path: filePath))
        {
            using (var csv = new CsvWriter(writer: writer
                                           , configuration: new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
                                           { HasHeaderRecord = true }))
            {
                csv.Context.RegisterClassMap<BankTransactionMap>();
                csv.WriteRecords(records: transactions);
            }
        }
    }
}