﻿using HomeFinances.Models;
using Microsoft.AspNetCore.Components.Forms;
using MudBlazor;
using HomeFinances.Components.Dialogs;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;

namespace HomeFinances.Services
{
    public class FileService
    {
        private readonly string uploadsPath;
        private readonly ChatGptApiService _chatGptApiService;
        private readonly FutureTransactionService _futureTransactionService;
        private readonly IDialogService _dialogService;
        private readonly ILogger<FileService> _logger;

        public FileService(
            ChatGptApiService chatGptApiService, 
            FutureTransactionService futureTransactionService,
            IDialogService dialogService,
            IWebHostEnvironment webHostEnvironment,
            ILogger<FileService> logger)
        {
            _chatGptApiService = chatGptApiService;
            _futureTransactionService = futureTransactionService;
            _dialogService = dialogService;
            _logger = logger;
            uploadsPath = Path.Combine(webHostEnvironment.ContentRootPath, "Data", "Uploads", "Receipts");
            _logger.LogInformation("Uploads path configured as: {Path}", uploadsPath);
        }

        public List<string> GetMostRecentFiles(int count = 5)
        {
            var directoryInfo = new DirectoryInfo(uploadsPath);
            // Assuming you're interested in image files, adjust the search pattern as needed
            var files = directoryInfo.GetFiles("*.png")
                .OrderByDescending(f => f.LastWriteTime)
                .Take(count)
                .Select(f => f.FullName)
                .ToList();
            return files;
        }

        public async Task<(FutureBankTransaction Transaction, string ReceiptPath)> ProcessReceiptUpload(IBrowserFile file)
        {
            try
            {
                var maxAllowedSize = 1024 * 1024 * 10; // 10 MB limit
                if (file.Size > maxAllowedSize)
                {
                    throw new InvalidOperationException("File size exceeds the maximum allowed limit of 10 MB.");
                }

                var buffer = new MemoryStream();
                await file.OpenReadStream(maxAllowedSize).CopyToAsync(buffer);
                buffer.Seek(0, SeekOrigin.Begin);

                _logger.LogInformation("Creating directory if it doesn't exist: {Path}", uploadsPath);
                Directory.CreateDirectory(uploadsPath);
                var savePath = Path.Combine(uploadsPath, $"{DateTime.Now:yyyy-MM-dd-HH-mm-ss}_{file.Name}");
                _logger.LogInformation("Saving file to: {Path}", savePath);

                using (var fileStream = File.Create(savePath))
                {
                    buffer.Seek(0, SeekOrigin.Begin);
                    await buffer.CopyToAsync(fileStream);
                }
                _logger.LogInformation("File saved successfully");

                // Get receipt text and categorization from ChatGPT
                var response = await _chatGptApiService.ExtractTextFromReceiptImage(savePath);
                var taxonomy = await _chatGptApiService.CategorizeTransactionFromReceiptImage(savePath);

                // Create future transaction with initial values
                var futureTransaction = new FutureBankTransaction
                {
                    Description = response,
                    TransactionDate = DateTime.Now,
                    TransactionAmount = -taxonomy.Amount, // Negative because it's an expense
                    Taxonomy = taxonomy,
                    Account = "Receipt Upload",
                    Balance = 0,
                    BankCode = "RECEIPT",
                    Id = Guid.NewGuid()
                };

                return (futureTransaction, savePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing receipt");
                throw new InvalidOperationException($"Error processing receipt: {ex.Message}", ex);
            }
        }

        public async Task SaveFutureTransaction(FutureBankTransaction transaction)
        {
            await _futureTransactionService.AddFutureTransactionAsync(transaction);
        }
    }
}
