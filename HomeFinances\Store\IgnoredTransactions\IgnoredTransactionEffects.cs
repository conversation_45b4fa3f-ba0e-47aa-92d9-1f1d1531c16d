﻿using Fluxor;
using HomeFinances.Services;

namespace HomeFinances.Store.IgnoredTransactions
{
    public class IgnoredTransactionEffects
    {
        private readonly IgnoredTransactionsService _transactionService;

        public IgnoredTransactionEffects(IgnoredTransactionsService transactionService)
        {
            _transactionService = transactionService;
        }

        [EffectMethod]
        public async Task HandleLoadIgnoredTransactions(IgnoredTransactionActions.LoadIgnoredTransactionsAction action, IDispatcher dispatcher)
        {
            try
            {
                var transactions = await _transactionService.LoadIgnoredTransactionsAsync();
                dispatcher.Dispatch(new IgnoredTransactionActions.LoadIgnoredTransactionsSuccessAction(transactions));
            }
            catch (Exception ex)
            {
                dispatcher.Dispatch(new IgnoredTransactionActions.IgnoreTransactionFailedAction(ex.Message));
            }
        }

        [EffectMethod]
        public async Task HandleIgnoreTransactionAction(IgnoredTransactionActions.IgnoreTransactionAction action, IDispatcher dispatcher)
        {
            try
            {
                await _transactionService.IgnoreTransaction(action.Transaction);
                dispatcher.Dispatch(new IgnoredTransactionActions.IgnoreTransactionSuccessAction(action.Transaction));
            }
            catch (Exception ex)
            {
                dispatcher.Dispatch(new IgnoredTransactionActions.IgnoreTransactionFailedAction(ex.Message));
            }
        }
    }
}