﻿using Azure.Storage.Blobs;
using CsvHelper;
using HomeFinances.Models;
using HomeFinances.Models.Mapping;
using System.Globalization;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace HomeFinances.Services
{
    public class FutureTransactionService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly string _containerName = "data";
        private readonly string _blobName = "FutureTransactions.csv";
        private readonly TransactionReadWriteService _transactionReadWriteService;
        private readonly ILogger<FutureTransactionService> _logger;
        private bool _isInitialized;

        public FutureTransactionService(
            TransactionReadWriteService transactionReadWriteService,
            BlobServiceClient blobServiceClient,
            ILogger<FutureTransactionService> logger)
        {
            _transactionReadWriteService = transactionReadWriteService;
            _blobServiceClient = blobServiceClient;
            _logger = logger;
            _logger.LogInformation("FutureTransactionService initialized with blob client: {BlobClientType}", _blobServiceClient.GetType().Name);
        }

        private async Task EnsureInitializedAsync()
        {
            if (_isInitialized) return;

            try
            {
                _logger.LogInformation("Ensuring container {ContainerName} exists", _containerName);
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                var containerExists = await containerClient.CreateIfNotExistsAsync();
                _logger.LogInformation("Container {ContainerName} exists: {ContainerExists}", _containerName, containerExists);

                // Check if the file exists, if not create an empty one
                var blobClient = containerClient.GetBlobClient(_blobName);
                var blobExists = await blobClient.ExistsAsync();
                _logger.LogInformation("Blob {BlobName} exists: {BlobExists}", _blobName, blobExists);

                if (!blobExists)
                {
                    _logger.LogInformation("Creating empty transactions file {BlobName}", _blobName);
                    using var stream = new MemoryStream();
                    using var writer = new StreamWriter(stream);
                    using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
                    csv.Context.RegisterClassMap<FutureTransactionMap>();
                    csv.WriteRecords(new List<FutureBankTransaction>());
                    await writer.FlushAsync();
                    stream.Position = 0;
                    await blobClient.UploadAsync(stream, overwrite: true);
                    _logger.LogInformation("Empty transactions file created successfully");
                }

                _isInitialized = true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ensuring container exists. Container: {ContainerName}, Blob: {BlobName}", _containerName, _blobName);
                throw;
            }
        }

        public async Task<List<FutureBankTransaction>> ReadTransactionsAsync()
        {
            try
            {
                await EnsureInitializedAsync();
                _logger.LogInformation("Reading transactions from blob {BlobName}", _blobName);
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                var blobClient = containerClient.GetBlobClient(_blobName);

                if (!await blobClient.ExistsAsync())
                {
                    _logger.LogInformation("Transactions file {BlobName} not found", _blobName);
                    return new List<FutureBankTransaction>();
                }

                using (var stream = new MemoryStream())
                {
                    await blobClient.DownloadToAsync(stream);
                    stream.Position = 0;
                    using (var reader = new StreamReader(stream))
                    using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
                    {
                        csv.Context.RegisterClassMap<FutureTransactionMap>();
                        var transactions = csv.GetRecords<FutureBankTransaction>().ToList();
                        _logger.LogInformation("Read {Count} transactions from blob", transactions.Count);
                        return transactions;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading transactions from blob {BlobName}", _blobName);
                return new List<FutureBankTransaction>();
            }
        }

        public async Task WriteTransactionsAsync(List<FutureBankTransaction> transactions)
        {
            try
            {
                await EnsureInitializedAsync();
                _logger.LogInformation("Writing {Count} transactions to blob {BlobName}", transactions.Count, _blobName);
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                var blobClient = containerClient.GetBlobClient(_blobName);

                using (var stream = new MemoryStream())
                using (var writer = new StreamWriter(stream))
                using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    csv.Context.RegisterClassMap<FutureTransactionMap>();
                    csv.WriteRecords(transactions);
                    await writer.FlushAsync();
                    stream.Position = 0;

                    _logger.LogInformation("Uploading {StreamLength} bytes to blob", stream.Length);
                    await blobClient.UploadAsync(stream, overwrite: true);
                    _logger.LogInformation("Successfully wrote transactions to blob");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing {Count} transactions to blob {BlobName}", transactions?.Count ?? 0, _blobName);
                throw;
            }
        }

        public async Task AddFutureTransactionAsync(FutureBankTransaction bankTransaction)
        {
            try
            {
                _logger.LogInformation("Adding new future transaction: Id={Id}, Description={Description}, Amount={Amount}, Date={Date}", 
                    bankTransaction.Id,
                    bankTransaction.Description,
                    bankTransaction.TransactionAmount,
                    bankTransaction.TransactionDate);

                var transactions = await ReadTransactionsAsync();
                _logger.LogInformation("Current transaction count: {Count}", transactions.Count);

                if (bankTransaction.Id == Guid.Empty)
                {
                    bankTransaction.Id = Guid.NewGuid();
                    _logger.LogInformation("Generated new ID for transaction: {TransactionId}", bankTransaction.Id);
                }

                transactions.Add(bankTransaction);
                _logger.LogInformation("Total transactions after add: {Count}", transactions.Count);
                await WriteTransactionsAsync(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding future transaction: {TransactionId}", bankTransaction?.Id);
                throw;
            }
        }

        public async Task DeleteTransactionAsync(FutureBankTransaction bankTransactionToDelete)
        {
            try
            {
                _logger.LogInformation("Deleting transaction: {TransactionId}", bankTransactionToDelete.Id);
                var transactions = await ReadTransactionsAsync();
                _logger.LogInformation("Found {Count} transactions before delete", transactions.Count);
                var updatedTransactions = transactions.Where(t => !AreTransactionsEquivalent(t, bankTransactionToDelete)).ToList();
                _logger.LogInformation("Remaining {Count} transactions after delete", updatedTransactions.Count);
                await WriteTransactionsAsync(updatedTransactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting transaction: {TransactionId}", bankTransactionToDelete?.Id);
                throw;
            }
        }

        public async Task UpdateTransactionAsync(FutureBankTransaction updatedBankTransaction)
        {
            try
            {
                _logger.LogInformation("Updating transaction: Id={Id}, Description={Description}, Amount={Amount}, Date={Date}", 
                    updatedBankTransaction.Id,
                    updatedBankTransaction.Description,
                    updatedBankTransaction.TransactionAmount,
                    updatedBankTransaction.TransactionDate);

                var transactions = await ReadTransactionsAsync();
                _logger.LogInformation("Found {Count} transactions before update", transactions.Count);

                var existingTransaction = transactions.FirstOrDefault(t => AreTransactionsEquivalent(t, updatedBankTransaction));
                if (existingTransaction != null)
                {
                    _logger.LogInformation("Found existing transaction to update: Id={Id}, Description={Description}, Amount={Amount}, Date={Date}",
                        existingTransaction.Id,
                        existingTransaction.Description,
                        existingTransaction.TransactionAmount,
                        existingTransaction.TransactionDate);
                }
                else
                {
                    _logger.LogWarning("No existing transaction found with ID {Id}", updatedBankTransaction.Id);
                }

                var updatedTransactions = transactions.Select(t =>
                {
                    if (AreTransactionsEquivalent(t, updatedBankTransaction))
                    {
                        _logger.LogInformation("Updating transaction {OldId} with new values", t.Id);
                        return updatedBankTransaction;
                    }
                    return t;
                }).ToList();

                await WriteTransactionsAsync(updatedTransactions);
                _logger.LogInformation("Successfully wrote {Count} transactions to storage", updatedTransactions.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating transaction: {TransactionId}", updatedBankTransaction?.Id);
                throw;
            }
        }

        public async Task ClearAllTransactionsAsync()
        {
            try
            {
                _logger.LogInformation("Clearing all transactions");
                await WriteTransactionsAsync(new List<FutureBankTransaction>());
                _logger.LogInformation("Successfully cleared all transactions");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing transactions");
                throw;
            }
        }

        private static bool AreTransactionsEquivalent(FutureBankTransaction x, FutureBankTransaction y)
        {
            return x.Id == y.Id;
        }
    }
}
