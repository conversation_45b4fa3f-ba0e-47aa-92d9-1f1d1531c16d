﻿using Fluxor;
using HomeFinances.Components.Dialogs;
using HomeFinances.Models;
using HomeFinances.Services;
using HomeFinances.Store.BankTransactions;
using HomeFinances.Store.FutureTransactions;
using HomeFinances.Store.BeeHiveTransactions;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;
namespace HomeFinances.Components.Pages
{
    public partial class FutureTransactions : ComponentBase,IDisposable
    {
        [Inject] private IState<BeeHiveTransactionState> BeehiveTransactionState { get; set; } = default!;
        [Inject] private IState<FutureTransactionState> FutureTransactionState { get; set; } = default!;
        [Inject] private IDispatcher Dispatcher { get; set; } = default!;
        [Inject] private FutureTransactionService FutureTransactionService { get; set; } = default!;
        [Inject] private IDialogService DialogService { get; set; } = default!;
        [Inject] private IJSRuntime JsRuntime { get; set; } = default!;
        private IEnumerable<FutureBankTransaction> futureTransactions => FutureTransactionState?.Value?.FutureTransactions?.ToList() ?? new List<FutureBankTransaction>();
        private HashSet<FutureBankTransaction> selectedTransactions = new();
        private bool isDeleting = false;

        private bool IsAllSelected => futureTransactions.Any() && selectedTransactions.Count == futureTransactions.Count();
        private bool IsIndeterminate => selectedTransactions.Count > 0 && selectedTransactions.Count < futureTransactions.Count();

        private void OnRowChecked(FutureBankTransaction item, bool isChecked)
        {
            if (isChecked)
                selectedTransactions.Add(item);
            else
                selectedTransactions.Remove(item);
        }

        private void ToggleSelectAll(bool isChecked)
        {
            if (isChecked)
                selectedTransactions = futureTransactions.ToHashSet();
            else
                selectedTransactions.Clear();
        }
        // Callback properties to enable method group binding in Razor without complex lambdas
        private Action<bool> ToggleSelectAllCallback => ToggleSelectAll;
        private Action<bool> GetRowCheckedCallback(FutureBankTransaction item) => value => OnRowChecked(item, value);

        private async Task DeleteSelected()
        {
            if (!selectedTransactions.Any())
                return;
            isDeleting = true;
            StateHasChanged();
            var itemsToDelete = selectedTransactions.ToList();
            selectedTransactions.Clear();
            foreach (var item in itemsToDelete)
            {
                await FutureTransactionService.DeleteTransactionAsync(item);
            }
            Dispatcher.Dispatch(new FutureTransactionActions.LoadFutureTransactionsAction());
            isDeleting = false;
            StateHasChanged();
        }
        private Double windowWidth;
        private IDialogReference? _dialogReference;
        // Sample category and subcategory data
        private List<string> Categories = new List<string> { "Food", "Utilities", "Entertainment" };
        private List<string> SubCategories = new List<string> { "Groceries", "Electricity", "Movies" };
        private bool _isInitialized = false;
        private BeeHiveTransaction? LatestTransaction
        {
            get
            {
                var latest = BeehiveTransactionState.Value.Transactions.OrderByDescending(t => t.TransactionDate).FirstOrDefault();
                Console.WriteLine($"LatestTransaction called. Count: {BeehiveTransactionState.Value.Transactions.Count()}, Latest: {latest?.Balance:C}");
                return latest;
            }
        }
        protected override void OnInitialized()
        {
            if (!_isInitialized)
            {
                _isInitialized = true;
                BeehiveTransactionState.StateChanged += OnStateHasChanged;
                FutureTransactionState.StateChanged += OnStateHasChanged;

                Console.WriteLine($"Initializing. BeehiveTransaction count: {BeehiveTransactionState.Value.Transactions.Count()}");
                var latest = BeehiveTransactionState.Value.Transactions.OrderByDescending(t => t.TransactionDate).FirstOrDefault();
                Console.WriteLine($"Initial latest transaction: {latest?.TransactionDate:MM/dd/yyyy} - {latest?.Balance:C}");

                if (BeehiveTransactionState.Value.LoadState.Status == LoadingStatusEnum.NotLoaded)
                {
                    Console.WriteLine("Dispatching LoadBeehiveTransactionsAction");
                    Dispatcher.Dispatch(new BeeHiveTransactionActions.LoadBeehiveTransactionsAction());
                }

                if (FutureTransactionState.Value.Loading.Status == LoadingStatusEnum.NotLoaded)
                {
                    Dispatcher.Dispatch(new FutureTransactionActions.LoadFutureTransactionsAction());
                }
            }

            base.OnInitialized();
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JsRuntime.InvokeVoidAsync("registerResizeListener", DotNetObjectReference.Create(this));
                await GetWindowWidth();
            }
        }

        [JSInvokable]
        public async Task OnBrowserResize()
        {
            await GetWindowWidth();
        }

        private async Task GetWindowWidth()
        {
            windowWidth = await JsRuntime.InvokeAsync<double>("blazorResize.getInnerWidth");
            StateHasChanged();
        }


        private Task<IEnumerable<string>> SearchCategories(string value)
        {
            return Task.FromResult(Categories.Where(x => x.Contains(value, StringComparison.InvariantCultureIgnoreCase)).AsEnumerable());
        }

        private Task<IEnumerable<string>> SearchSubCategories(string value)
        {
            return Task.FromResult(SubCategories.Where(x => x.Contains(value, StringComparison.InvariantCultureIgnoreCase)).AsEnumerable());
        }

        private void OnDateChanged( DateTime? newDate, FutureBankTransaction bankTransactionToUpdate)
        {
            var updatedTransaction = bankTransactionToUpdate as FutureBankTransaction;
            updatedTransaction.TransactionDate = newDate;
            Dispatcher.Dispatch(new FutureTransactionActions.UpdateFutureTransactionAction(updatedTransaction));
        }

        private async Task OnDescriptionChanged(string newValue, FutureBankTransaction context)
        {
            context.Description = newValue;
            await UpdateBankTransaction(context);
        }

        private async Task OnCategoryChanged(string newValue, FutureBankTransaction context)
        {
            context.Taxonomy.Category = newValue;
            Console.WriteLine($"Category changed to: {newValue} for transaction ID {context.Id}");
            await UpdateBankTransaction(context);
        }
        private async Task OnAmountChanged(decimal newValue, FutureBankTransaction context)
        {
            context.TransactionAmount = newValue;
            Console.WriteLine($"Category changed to: {newValue} for transaction ID {context.Id}");
            await UpdateBankTransaction(context);
        }
        private async Task OnSubCategoryChanged(string newValue, FutureBankTransaction context)
        {
            context.Taxonomy.SubCategory = newValue;
            Console.WriteLine($"SubCategory changed to: {newValue} for transaction ID {context.Id}");
            await UpdateBankTransaction(context);
        }

        private async Task UpdateBankTransaction(FutureBankTransaction transaction)
        {
            Dispatcher.Dispatch(new FutureTransactionActions.UpdateFutureTransactionAction(transaction));
        }
        private string TruncateDescription(string description)
        {
            if (string.IsNullOrEmpty(description)) return string.Empty;
            return description.Length > 10 ? description.Substring(0, 10) + "…" : description;
        }
        private void OnStateHasChanged(object? sender, EventArgs e)
        {
            StateHasChanged();
        }
        private void DeleteItem(FutureBankTransaction bankTransactionToDelete)
        {
            Dispatcher.Dispatch(new FutureTransactionActions.DeleteFutureTransactionAction(bankTransactionToDelete));
        }

        private string GetEnabledIcon(FutureBankTransaction bankTransaction)
        {
            return bankTransaction.Enabled ? Icons.Material.Rounded.CheckBox : Icons.Material.Rounded.CheckBoxOutlineBlank;
        }
        private void ToggleEnabled(FutureBankTransaction bankTransactionToToggle)
        {
            var updatedTransaction = bankTransactionToToggle as FutureBankTransaction;
            updatedTransaction.Enabled = !updatedTransaction.Enabled;
            InvokeAsync(StateHasChanged);
            Dispatcher.Dispatch(new FutureTransactionActions.UpdateFutureTransactionAction(updatedTransaction));
        }

        public static bool AreTransactionsEquivalent(FutureBankTransaction x, FutureBankTransaction y)
        {
            if (x == null && y == null) return true;
            if (x == null || y == null) return false;

            return x.TransactionAmount == y.TransactionAmount &&
                   string.Equals(x.Description, y.Description);
        }

        private void ShowAddTransactionDialog()
        {
            var options = new DialogOptions { CloseButton = false, MaxWidth = MaxWidth.Small, FullWidth = true }; 
            var parameters = new DialogParameters();

            _dialogReference = DialogService.Show<AddFutureTransaction>("Add Future Transaction", parameters,options);

            InvokeAsync(StateHasChanged);
        }
        private void CloseDialog()
        {
            _dialogReference?.Close();
            InvokeAsync(StateHasChanged);
        }
        protected  void Dispose(bool disposed)
        {
            BeehiveTransactionState.StateChanged -= OnStateHasChanged;
            FutureTransactionState.StateChanged -= OnStateHasChanged;
        }

        public void Dispose()
        {
            BeehiveTransactionState.StateChanged -= OnStateHasChanged;
            FutureTransactionState.StateChanged -= OnStateHasChanged;
        }

        private async Task ShowClearAllDialog()
        {
            var parameters = new DialogParameters();
            var dialog = await DialogService.ShowAsync<ClearFutureTransactionsDialog>("Clear All Future Transactions", parameters);
            var result = await dialog.Result;

            if (!result.Canceled)
            {
                await LoadData();
            }
        }

        private async Task LoadData()
        {
            // Reload the future transactions state
            await Task.CompletedTask; // This will be handled by the Fluxor state management
            StateHasChanged();
        }
    }
}
