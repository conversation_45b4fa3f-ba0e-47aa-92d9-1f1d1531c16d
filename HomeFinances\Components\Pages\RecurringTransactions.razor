@page "/recurring-transactions"
@rendermode InteractiveServer
@using HomeFinances.Models
@using HomeFinances.Components.Dialogs
@using HomeFinances.Store.RecurringTransactions
@using Fluxor

<MudExpansionPanels>
    <MudExpansionPanel>
        <TitleContent>
            <div class="d-flex align-center">
                <MudText Typo="Typo.h5">Recurring Transactions (@(RecurringTransactionsState.Value.RecurringTransactions.Count()))</MudText>
                @if (RecurringTransactionsState.Value.Loading.Status != LoadingStatusEnum.Loaded)
                {
                    <MudProgressCircular Size="Size.Small" Color="Color.Primary" Indeterminate="true" Class="ml-2" />
                }
            </div>
        </TitleContent>
        <ChildContent>
            @if (RecurringTransactionsState.Value.Loading.Status == LoadingStatusEnum.Error)
            {
                <MudText Color="Color.Error">An error occurred while loading transactions.</MudText>
            }
            else if (RecurringTransactionsState.Value.Loading.Status != LoadingStatusEnum.Loaded)
            {
                <MudProgressCircular Indeterminate="true" />
            }
            else
            {
                <MudStack>
                    <MudTable Items="@_recurringTransactions" Dense="true" Hover="true" Breakpoint="Breakpoint.Sm">
                        <HeaderContent>
                            <MudTh>Description</MudTh>
                            <MudTh>Day of Month</MudTh>
                            <MudTh>Amount</MudTh>
                            <MudTh>End Date</MudTh>
                            <MudTh>Outgo</MudTh>
                            <MudTh></MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="Description">@context.Description</MudTd>
                            <MudTd DataLabel="Day of Month">@context.DayOfMonth</MudTd>
                            <MudTd DataLabel="Amount">@context.Amount.ToString("C")</MudTd>
                            <MudTd DataLabel="End Date">@(context.EndDate == default ? "Never" : context.EndDate.ToShortDateString())</MudTd>
                            <MudTd DataLabel="Outgo">@context.IsOutgo</MudTd>
                            <MudTd>
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" OnClick="@(() => DeleteTransaction(context))" />
                            </MudTd>
                        </RowTemplate>
                    </MudTable>
                    
                </MudStack>
            }
        </ChildContent>
    </MudExpansionPanel>
</MudExpansionPanels> 