﻿using Fluxor;
using HomeFinances.Models;
using HomeFinances.Services;
using Microsoft.Extensions.Logging;
using static HomeFinances.Store.FutureTransactions.FutureTransactionActions;

namespace HomeFinances.Store.FutureTransactions
{
    public class FutureTransactionEffects
    {
        private readonly TransactionReadWriteService _transactionReadWriteService;
        private readonly CategoryService _categoryService;
        private readonly FutureTransactionService _futureTransactionService;
        private readonly ILogger<FutureTransactionEffects> _logger;

        public FutureTransactionEffects(
            TransactionReadWriteService transactionReadWriteService, 
            CategoryService categoryService, 
            FutureTransactionService futureTransactionService,
            ILogger<FutureTransactionEffects> logger)
        {
            _transactionReadWriteService = transactionReadWriteService;
            _categoryService = categoryService;
            _futureTransactionService = futureTransactionService;
            _logger = logger;
        }

        [EffectMethod]
        public async Task HandleDeleteFutureTransactionAction(DeleteFutureTransactionAction action, IDispatcher dispatcher)
        {
            try
            {
                _logger.LogInformation("Handling DeleteFutureTransactionAction for transaction {TransactionId}", action.transaction.Id);
                await _futureTransactionService.DeleteTransactionAsync(action.transaction);
                var allTransactions = await _futureTransactionService.ReadTransactionsAsync();
                var updatedTransactions = await UpdateTransactionsAndBalances(allTransactions);
                _logger.LogInformation("Successfully deleted transaction {TransactionId}", action.transaction.Id);
                dispatcher.Dispatch(new DeleteFutureTransactionSucceededAction(updatedTransactions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete transaction {TransactionId}", action.transaction.Id);
                dispatcher.Dispatch(new DeleteFutureTransactionFailedAction(ex.Message));
            }
        }

        [EffectMethod]
        public async Task HandleUpdateFutureTransactionAction(UpdateFutureTransactionAction action, IDispatcher dispatcher)
        {
            try
            {
                var transaction = action.transaction;
                _logger.LogWarning("=== START: Effect Handler - Update Transaction ===");
                _logger.LogWarning("Received transaction in effect handler: Id={Id}, Description={Description}, Amount={Amount:C}, Date={Date:d}, IsNew={IsNew}", 
                    transaction?.Id,
                    transaction?.Description,
                    transaction?.TransactionAmount,
                    transaction?.TransactionDate,
                    transaction?.Id == Guid.Empty);
                
                if (transaction?.Id == null || transaction.Id == Guid.Empty)
                {
                    transaction.Id = Guid.NewGuid();
                    _logger.LogWarning("Generated new ID {Id} for transaction", transaction.Id);
                    _logger.LogWarning("Adding new transaction to service: Description={Description}, Amount={Amount:C}, Date={Date:d}", 
                        transaction.Description, transaction.TransactionAmount, transaction.TransactionDate);
                    await _futureTransactionService.AddFutureTransactionAsync(transaction);
                    _logger.LogWarning("Successfully added new transaction with ID {Id}", transaction.Id);
                }
                else
                {
                    _logger.LogWarning("Updating existing transaction {Id}", transaction.Id);
                    await _futureTransactionService.UpdateTransactionAsync(transaction);
                    _logger.LogWarning("Successfully updated transaction {Id}", transaction.Id);
                }

                if (transaction?.Taxonomy != null)
                {
                    _logger.LogWarning("Adding category: Category={Category}, SubCategory={SubCategory}", 
                        transaction.Taxonomy?.Category, transaction.Taxonomy?.SubCategory);
                    _categoryService.AddCategory(transaction.Taxonomy?.Category, transaction.Taxonomy?.SubCategory);
                }

                // Read back all transactions and update their balances
                _logger.LogWarning("Reading back all transactions from service");
                var allTransactions = await _futureTransactionService.ReadTransactionsAsync();
                _logger.LogWarning("Retrieved {Count} transactions after add/update", allTransactions.Count);
                
                _logger.LogWarning("Updating balances for all transactions");
                var updatedTransactions = await UpdateTransactionsAndBalances(allTransactions);
                _logger.LogWarning("Successfully updated balances for {Count} transactions", updatedTransactions.Count);
                
                // Dispatch success with the updated transactions
                _logger.LogWarning("Dispatching UpdateFutureTransactionSucceededAction with {Count} transactions", updatedTransactions.Count);
                dispatcher.Dispatch(new UpdateFutureTransactionSucceededAction(updatedTransactions));
                _logger.LogWarning("=== END: Effect Handler - Update Transaction ===");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to update transaction {TransactionId}: {Error}", action.transaction?.Id, ex.Message);
                dispatcher.Dispatch(new UpdateFutureTransactionFailedAction(ex.Message));
            }
        }

        [EffectMethod]
        public async Task HandleLoadFutureTransactionsAction(LoadFutureTransactionsAction action, IDispatcher dispatcher)
        {
            try
            {
                _logger.LogInformation("Handling LoadFutureTransactionsAction");
                var transactions = await _futureTransactionService.ReadTransactionsAsync();
                _logger.LogInformation("Retrieved {Count} future transactions", transactions.Count);
                var updatedTransactions = await UpdateTransactionsAndBalances(transactions);
                dispatcher.Dispatch(new LoadFutureTransactionsSuccessAction(updatedTransactions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load future transactions");
                dispatcher.Dispatch(new LoadFutureTransactionsFailedAction(ex.Message));
            }
        }

        [EffectMethod]
        public async Task HandleClearAllFutureTransactionsAction(ClearAllFutureTransactionsAction action, IDispatcher dispatcher)
        {
            try
            {
                _logger.LogInformation("Handling ClearAllFutureTransactionsAction");
                await _futureTransactionService.ClearAllTransactionsAsync();
                _logger.LogInformation("Successfully cleared all future transactions");
                dispatcher.Dispatch(new ClearAllFutureTransactionsSucceededAction());
                dispatcher.Dispatch(new LoadFutureTransactionsAction());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear all future transactions");
                dispatcher.Dispatch(new ClearAllFutureTransactionsFailedAction(ex.Message));
            }
        }

        private async Task<List<FutureBankTransaction>> UpdateTransactionsAndBalances(List<FutureBankTransaction> futureTransactions)
        {
            _logger.LogInformation("Updating balances for {Count} future transactions", futureTransactions.Count);
            var transactionsInChronologicalOrder = futureTransactions
                .OrderBy(t => t.TransactionDate)
                .ThenByDescending(t => t.TransactionAmount > 0)
                .ToList();

            var balance = await _transactionReadWriteService.GetBeeHiveCurrentBalanceAsync();
            foreach (var transaction in transactionsInChronologicalOrder)
            {
                if (transaction.Enabled)
                {
                    balance += transaction.TransactionAmount;
                }
                transaction.ProjectedBalance = balance;
            }

            _logger.LogInformation("Finished updating balances for future transactions");
            return transactionsInChronologicalOrder;
        }
    }
}
