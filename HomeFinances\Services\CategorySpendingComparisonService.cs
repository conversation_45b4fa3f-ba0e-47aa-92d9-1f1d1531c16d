﻿using HomeFinances.Models;

namespace HomeFinances.Services
{
    public class CategorySpendingComparisonService(FutureTransactionService futureTransactionService, TransactionReadWriteService transactionReadWriteService, CategoryMappingService categoryMappingService)
    {
        public async Task<List<CategoryComparison>> GetCategoryComparisons(DateTime? startDate = null)
        {
            var today = DateTime.Today;
            DateTime startOfCurrentMonth = new DateTime(today.Year, today.Month, 1);
            DateTime startOfPreviousMonth = startOfCurrentMonth.AddMonths(-1);
            DateTime endOfPreviousMonth = startOfCurrentMonth.AddDays(-1);

            DateTime comparisonStartDate = startDate ?? startOfPreviousMonth;
            DateTime comparisonEndDate = startDate.HasValue ? new DateTime(startDate.Value.Year, startDate.Value.Month, 1).AddMonths(1).AddDays(-1) : endOfPreviousMonth;

            var bankTransactions = await transactionReadWriteService.ReadBankTransactionsAsync("BankTransactions.csv");
            var pastTransactions = bankTransactions
                .Where(t => t.TransactionDate >= comparisonStartDate && t.TransactionDate <= comparisonEndDate)
                .ToList();

            // Assume next date range is current month if no start date is provided, otherwise the next month of the given start date
            var futureStartDate = startDate.HasValue ? new DateTime(startDate.Value.Year, startDate.Value.Month, 1).AddMonths(1) : startOfCurrentMonth;
            var futureEndDate = futureStartDate.AddMonths(1).AddDays(-1);

            var futureTransactions = (await futureTransactionService.ReadTransactionsAsync())
                .Where(t => t.TransactionDate >= futureStartDate && t.TransactionDate <= futureEndDate)
                .ToList();

            // Aggregate past transactions by category
            var pastTotalsByCategory = pastTransactions
                .GroupBy(t => t?.Taxonomy?.Category ?? "Unknown")
                .Select(g => new CategoryTotal(g.Key, g.Sum(t => t.TransactionAmount)))
                .ToList();

            // Aggregate future transactions by category
            var futureTotalsByCategory = futureTransactions
                .GroupBy(t => t.Taxonomy.Category)
                .Select(g => new CategoryTotal(g.Key, g.Sum(t => t.TransactionAmount)))
                .ToList();

            // Combine the totals to get comparisons
            var allCategories = pastTotalsByCategory.Select(pt => pt.Category)
                .Union(futureTotalsByCategory.Select(ft => ft.Category))
                .Distinct();

            var combinedCategories = allCategories.Select(category => new CategoryComparison
            {
                Category = category,
                BankTotal = pastTotalsByCategory.Where(pt => pt.Category == category).Sum(pt => pt.Total),
                FutureTotal = futureTotalsByCategory.Where(ft => ft.Category == category).Sum(ft => ft.Total)
            }).ToList();

            return combinedCategories;
        }
    }
}

