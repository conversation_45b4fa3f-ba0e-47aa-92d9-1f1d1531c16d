﻿@page "/"
@page "/all-transactions"
@rendermode InteractiveServer
@attribute [StreamRendering]
@using HomeFinances.Services
@using System.Globalization
@using System.Text
@using System.IO
@using CsvHelper
@using CsvHelper.Configuration
@using HomeFinances.Models
@using HomeFinances.Models.Mapping
@inject TransactionReadWriteService TransactionService
@inject RecurringTransactionService RecurringTransactionService
@inject FutureTransactionService FutureTransactionService
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<MudThemeProvider />
<MudDialogProvider />
<MudSnackbarProvider />
<Fluxor.Blazor.Web.StoreInitializer />

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-0">
    <div class="main-content">
        <MudStack Row="true" Spacing="2" Class="mb-4">
            <FileUploadButton />
            <ReceiptUploader />
            <MudButton Variant="Variant.Filled"
                      Color="Color.Info"
                      StartIcon="@Icons.Material.Filled.Download"
                      OnClick="ExportBankTransactions">
                Export Bank Transactions
            </MudButton>
            <MudButton Variant="Variant.Filled"
                      Color="Color.Info"
                      StartIcon="@Icons.Material.Filled.Download"
                      OnClick="ExportRecurringTransactions">
                Export Recurring Transactions
            </MudButton>
            <InputFile id="recurringFileInput" OnChange="ImportRecurringTransactions" hidden accept=".csv" />
            <MudButton Variant="Variant.Filled"
                      Color="Color.Primary"
                      StartIcon="@Icons.Material.Filled.Upload"
                      OnClick="@(() => JSRuntime.InvokeVoidAsync("eval", "document.getElementById('recurringFileInput').click();"))">
                Import Recurring Transactions
            </MudButton>
        </MudStack>
        <MudStack Spacing="2">
            <PossiblyMissingTransactions />
            <FutureTransactions />
            <BankTransactions />
            <RecurringTransactions />
            <!-- Loans section removed -->
        </MudStack>
    </div>
</MudContainer>

@code {
    private async Task ExportBankTransactions()
    {
        try
        {
            var transactions = await TransactionService.ReadBankTransactionsAsync("BankTransactions.csv");

            if (!transactions.Any())
            {
                Snackbar.Add("No transactions to export", Severity.Warning);
                return;
            }

            var csvContent = new StringBuilder();
            using (var writer = new StringWriter())
            using (var csv = new CsvHelper.CsvWriter(writer, new CsvHelper.Configuration.CsvConfiguration(CultureInfo.InvariantCulture) { HasHeaderRecord = true }))
            {
                csv.Context.RegisterClassMap<HomeFinances.Models.Mapping.BankTransactionMap>();
                csv.WriteRecords(transactions);
                csvContent.Append(writer.ToString());
            }

            var fileName = $"BankTransactions_{DateTime.Now:yyyy-MM-dd-HH-mm}.csv";
            var bytes = Encoding.UTF8.GetBytes(csvContent.ToString());

            // Create a data URL for the CSV content
            var dataUrl = $"data:text/csv;charset=utf-8;base64,{Convert.ToBase64String(bytes)}";

            // Use vanilla JavaScript to trigger the download
            await JSRuntime.InvokeVoidAsync("eval", @"
                (function() {
                    const link = document.createElement('a');
                    link.download = '" + fileName + @"';
                    link.href = '" + dataUrl + @"';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })();
            ");

            Snackbar.Add("Bank transactions exported successfully", Severity.Success);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Export error: {ex.Message}");
            Snackbar.Add($"Error exporting transactions: {ex.Message}", Severity.Error);
        }
    }

    private async Task ExportRecurringTransactions()
    {
        try
        {
            var transactions = await RecurringTransactionService.LoadTransactionsAsync();

            if (!transactions.Any())
            {
                Snackbar.Add("No recurring transactions to export", Severity.Warning);
                return;
            }

            var csvContent = new StringBuilder();
            using (var writer = new StringWriter())
            using (var csv = new CsvHelper.CsvWriter(writer, new CsvHelper.Configuration.CsvConfiguration(CultureInfo.InvariantCulture) { HasHeaderRecord = true }))
            {
                // Write headers manually since we don't have a mapping class for recurring transactions
                csv.WriteField("Id");
                csv.WriteField("Description");
                csv.WriteField("Amount");
                csv.WriteField("DayOfMonth");
                csv.WriteField("IsOutgo");
                csv.WriteField("EndDate");
                csv.WriteField("IgnoreUntil");
                csv.NextRecord();

                // Write records
                foreach (var transaction in transactions)
                {
                    csv.WriteField(transaction.Id);
                    csv.WriteField(transaction.Description);
                    csv.WriteField(transaction.Amount);
                    csv.WriteField(transaction.DayOfMonth);
                    csv.WriteField(transaction.IsOutgo);
                    csv.WriteField(transaction.EndDate);
                    csv.WriteField(transaction.IgnoreUntil);
                    csv.NextRecord();
                }

                csvContent.Append(writer.ToString());
            }

            var fileName = $"RecurringTransactions_{DateTime.Now:yyyy-MM-dd-HH-mm}.csv";
            var bytes = Encoding.UTF8.GetBytes(csvContent.ToString());

            // Create a data URL for the CSV content
            var dataUrl = $"data:text/csv;charset=utf-8;base64,{Convert.ToBase64String(bytes)}";

            // Use vanilla JavaScript to trigger the download
            await JSRuntime.InvokeVoidAsync("eval", @"
                (function() {
                    const link = document.createElement('a');
                    link.download = '" + fileName + @"';
                    link.href = '" + dataUrl + @"';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })();
            ");

            Snackbar.Add("Recurring transactions exported successfully", Severity.Success);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Export error: {ex.Message}");
            Snackbar.Add($"Error exporting transactions: {ex.Message}", Severity.Error);
        }
    }

    private async Task ImportFutureTransactions(InputFileChangeEventArgs e)
    {
        try
        {
            var file = e.File;
            if (file == null)
            {
                Snackbar.Add("No file selected", Severity.Warning);
                return;
            }

            if (file.ContentType != "text/csv" && !file.Name.EndsWith(".csv"))
            {
                Snackbar.Add("Please select a CSV file", Severity.Warning);
                return;
            }

            using var stream = file.OpenReadStream(maxAllowedSize: 1024 * 1024); // 1MB max size
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);
            memoryStream.Position = 0;

            using var reader = new StreamReader(memoryStream);
            using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture));

            try
            {
                // Validate the CSV format by registering the map and attempting to read
                csv.Context.RegisterClassMap<FutureTransactionMap>();
                var transactions = csv.GetRecords<FutureBankTransaction>().ToList();

                if (!transactions.Any())
                {
                    Snackbar.Add("The file contains no transactions", Severity.Warning);
                    return;
                }

                // Clear existing transactions and write new ones
                await FutureTransactionService.ClearAllTransactionsAsync();
                await FutureTransactionService.WriteTransactionsAsync(transactions);

                Snackbar.Add($"Successfully imported {transactions.Count} future transactions", Severity.Success);
            }
            catch (HeaderValidationException)
            {
                Snackbar.Add("Invalid CSV format. Please ensure the file has the correct headers: Id, TransactionAmount, Description, Category, SubCategory, Confidence, TransactionDate, Enabled", Severity.Error);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error parsing CSV: {ex.Message}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error importing file: {ex.Message}", Severity.Error);
        }
    }

    private async Task ImportRecurringTransactions(InputFileChangeEventArgs e)
    {
        try
        {
            var file = e.File;
            if (file == null)
            {
                Snackbar.Add("No file selected", Severity.Warning);
                return;
            }

            if (file.ContentType != "text/csv" && !file.Name.EndsWith(".csv"))
            {
                Snackbar.Add("Please select a CSV file", Severity.Warning);
                return;
            }

            using var stream = file.OpenReadStream(maxAllowedSize: 1024 * 1024); // 1MB max size
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);
            memoryStream.Position = 0;

            using var reader = new StreamReader(memoryStream);
            using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture));

            try
            {
                // Read headers and validate them
                csv.Read();
                csv.ReadHeader();
                var headers = csv.HeaderRecord;
                var expectedHeaders = new[] { "Id", "Description", "Amount", "DayOfMonth", "IsOutgo", "EndDate", "IgnoreUntil" };

                if (!expectedHeaders.All(h => headers.Contains(h)))
                {
                    Snackbar.Add($"Invalid CSV format. Please ensure the file has these headers: {string.Join(", ", expectedHeaders)}", Severity.Error);
                    return;
                }

                var transactions = new List<RecurringTransaction>();

                // Read records
                while (csv.Read())
                {
                    var transaction = new RecurringTransaction(); // This will create a new GUID automatically

                    try
                    {
                        transaction.Description = csv.GetField("Description") ?? string.Empty;
                        transaction.Amount = csv.GetField<decimal>("Amount");
                        transaction.DayOfMonth = csv.GetField<int>("DayOfMonth");
                        transaction.IsOutgo = csv.GetField<bool>("IsOutgo");

                        // Handle dates
                        var endDateStr = csv.GetField("EndDate");
                        if (DateTime.TryParse(endDateStr, out var endDate))
                        {
                            transaction.EndDateTime = endDate;
                        }

                        var ignoreUntilStr = csv.GetField("IgnoreUntil");
                        if (DateTime.TryParse(ignoreUntilStr, out var ignoreUntil))
                        {
                            transaction.IgnoreUntil = ignoreUntil;
                        }
                    }
                    catch (Exception ex)
                    {
                        Snackbar.Add($"Error parsing row: {ex.Message}", Severity.Error);
                        continue; // Skip this row and continue with the next
                    }

                    transactions.Add(transaction);
                }

                if (!transactions.Any())
                {
                    Snackbar.Add("The file contains no valid transactions", Severity.Warning);
                    return;
                }

                // Save the transactions
                await RecurringTransactionService.SaveTransactionsAsync(transactions);

                Snackbar.Add($"Successfully imported {transactions.Count} recurring transactions", Severity.Success);
            }
            catch (HeaderValidationException)
            {
                Snackbar.Add("Invalid CSV format. Please ensure the file has the correct headers: Id, Description, Amount, DayOfMonth, IsOutgo, EndDate, IgnoreUntil", Severity.Error);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error parsing CSV: {ex.Message}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error importing file: {ex.Message}", Severity.Error);
        }
    }
}

<style>
    .main-content {
        padding: 1rem;
        background-color: #f0f8ff;
        min-height: 100vh;
        width: 100%;
    }

    ::deep .mud-expansion-panel {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        margin-bottom: 0.5rem;
        background-color: white;
        width: 100%;
    }

    ::deep .mud-expand-panel-header {
        background-color: #fafafa;
    }

    ::deep .mud-stack {
        width: 100%;
    }
</style>