using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Amazon.Runtime;
using CSharpFunctionalExtensions;
using Fluxor;
using HomeFinances.Store.Account;

namespace HomeFinances.Repositories;

/// <summary>
///     AWS DynamoDB implementation of ICategoryRepository.
///     Table schema: Primary Key = Category (string), SubCategories = SS (string set).
/// </summary>
public class AwsDynamoCategoryRepository : ICategoryRepository
{
    private const string TableName = "Categories";
    private readonly IAmazonDynamoDB _dynamoDb;
    private readonly FileCategoryRepository _fallbackRepository;
    private readonly ILogger<AwsDynamoCategoryRepository> _logger;
    private readonly bool _useAws;
    private bool _tableVerified;

    public AwsDynamoCategoryRepository(IAmazonDynamoDB dynamoDb
        , IState<AccountState> accountStateAccessor
        , ILogger<AwsDynamoCategoryRepository> logger)
    {
        _dynamoDb = dynamoDb;
        _logger = logger ?? throw new ArgumentNullException(paramName: nameof(logger));
        _useAws = dynamoDb != null;

        // Always initialize the fallback repository for resilience
        try
        {
            _fallbackRepository = new FileCategoryRepository(accountStateAccessor: accountStateAccessor);
            if (!_useAws) _logger.LogWarning(message: "DynamoDB client is null. Using file-based repository as fallback.");
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message:
                             "Failed to initialize fallback FileCategoryRepository. Some operations may fail if AWS is unavailable.");
            throw; // This is critical - we need at least one working repository
        }
    }

    public async Task<Result<List<string>>> GetAllCategoriesAsync()
    {
        if (!_useAws)
        {
            _logger.LogInformation(message: "Using file-based repository for GetAllCategoriesAsync");
            var fallbackResult = await _fallbackRepository.GetAllCategoriesAsync();
            return fallbackResult;
        }

        try
        {
            // Verify table exists before attempting operations
            if (!await VerifyTableExistsAsync().ConfigureAwait(continueOnCapturedContext: false))
            {
                _logger.LogWarning(message: "DynamoDB table verification failed. Using file-based repository as fallback.");
                var fallbackResult = await _fallbackRepository.GetAllCategoriesAsync();
                return fallbackResult;
            }

            var request = new ScanRequest { TableName = TableName };
            var response = await _dynamoDb.ScanAsync(request: request).ConfigureAwait(continueOnCapturedContext: false);

            if (response == null)
            {
                _logger.LogWarning(
                    message: "DynamoDB ScanAsync returned null response. Using file-based repository as fallback.");
                var fallbackResult = await _fallbackRepository.GetAllCategoriesAsync();
                return fallbackResult;
            }

            var result = new List<string>();

            if (response.Items == null)
            {
                _logger.LogWarning(message: "DynamoDB ScanAsync returned null Items collection. Returning empty list.");
                return Result.Success(value: result);
            }

            foreach (var item in response.Items)
            {
                if (item == null) continue;

                if (item.TryGetValue(key: "Category"
                                     , value: out var catVal) && catVal?.S != null)
                {
                    var category = catVal.S;
                    result.Add(item: category);
                }
            }

            _logger.LogInformation(message: "Successfully retrieved {Count} categories from DynamoDB"
                                   , result.Count);
            return Result.Success(value: result);
        }
        catch (ResourceNotFoundException)
        {
            _logger.LogWarning(message: "DynamoDB table {TableName} not found. Using file-based repository as fallback."
                               , TableName);
            var fallbackResult = await _fallbackRepository.GetAllCategoriesAsync();
            return fallbackResult;
        }
        catch (ProvisionedThroughputExceededException ptEx)
        {
            _logger.LogWarning(exception: ptEx
                               , message: "DynamoDB provisioned throughput exceeded. Using file-based repository as fallback.");
            var fallbackResult = await _fallbackRepository.GetAllCategoriesAsync();
            return fallbackResult;
        }
        catch (AmazonServiceException asEx)
        {
            _logger.LogError(exception: asEx
                             , message: "AWS service error accessing DynamoDB. Using file-based repository as fallback.");
            var fallbackResult = await _fallbackRepository.GetAllCategoriesAsync();
            return fallbackResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Unexpected error accessing DynamoDB. Using file-based repository as fallback.");
            var fallbackResult = await _fallbackRepository.GetAllCategoriesAsync();
            return fallbackResult;
        }
    }

    public async Task<Result> AddCategoryAsync(string category)
    {
        // Implementation for adding a category
        // This method needs to be implemented as per the interface requirement
        return Result.Failure(error: "Not implemented");
    }

    public async Task<Result<List<string>>> GetSubCategoriesAsync(string category)
    {
        if (string.IsNullOrWhiteSpace(value: category))
        {
            _logger.LogWarning(message: "GetSubCategoriesAsync called with null or empty category. Returning empty list.");
            return Result.Success(value: new List<string>());
        }

        if (!_useAws)
        {
            _logger.LogInformation(message: "Using file-based repository for GetSubCategoriesAsync");
            var fallbackResult = await _fallbackRepository.GetSubCategoriesAsync(category: category);
            return fallbackResult;
        }

        try
        {
            // Verify table exists before attempting operations
            if (!await VerifyTableExistsAsync().ConfigureAwait(continueOnCapturedContext: false))
            {
                _logger.LogWarning(message: "DynamoDB table verification failed. Using file-based repository as fallback.");
                var fallbackResult = await _fallbackRepository.GetSubCategoriesAsync(category: category);
                return fallbackResult;
            }

            var request = new GetItemRequest
            {
                TableName = TableName,
                Key = new Dictionary<string, AttributeValue>
                {
                    [key: "Category"] = new() { S = category }
                }
                              ,
                ConsistentRead = true // Ensure we get the most up-to-date data
            };

            var response = await _dynamoDb.GetItemAsync(request: request).ConfigureAwait(continueOnCapturedContext: false);

            if (response == null)
            {
                _logger.LogWarning(
                    message: "DynamoDB GetItemAsync returned null response. Using file-based repository as fallback.");
                var fallbackResult = await _fallbackRepository.GetSubCategoriesAsync(category: category);
                return fallbackResult;
            }

            if (response.Item == null || response.Item.Count == 0)
            {
                _logger.LogInformation(message: "No item found in DynamoDB for category: {Category}"
                                       , category);
                return Result.Success(value: new List<string>());
            }

            if (response.Item.TryGetValue(key: "SubCategories"
                                          , value: out var subVal) && subVal?.SS != null)
                return
                    Result.Success(
                        value: new List<string>(
                            collection: subVal.SS)); // Create a new list to avoid potential modification issues

            return Result.Success(value: new List<string>());
        }
        catch (ResourceNotFoundException)
        {
            _logger.LogWarning(message: "DynamoDB table {TableName} not found. Using file-based repository as fallback."
                               , TableName);
            var fallbackResult = await _fallbackRepository.GetSubCategoriesAsync(category: category);
            return fallbackResult;
        }
        catch (ProvisionedThroughputExceededException ptEx)
        {
            _logger.LogWarning(exception: ptEx
                               , message: "DynamoDB provisioned throughput exceeded. Using file-based repository as fallback.");
            var fallbackResult = await _fallbackRepository.GetSubCategoriesAsync(category: category);
            return fallbackResult;
        }
        catch (AmazonServiceException asEx)
        {
            _logger.LogError(exception: asEx
                             , message: "AWS service error accessing DynamoDB. Using file-based repository as fallback.");
            var fallbackResult = await _fallbackRepository.GetSubCategoriesAsync(category: category);
            return fallbackResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Unexpected error accessing DynamoDB. Using file-based repository as fallback.");
            var fallbackResult = await _fallbackRepository.GetSubCategoriesAsync(category: category);
            return fallbackResult;
        }
    }

    public async Task<Result> AddSubCategoryAsync(string category
        , string subCategory)
    {
        if (string.IsNullOrWhiteSpace(value: category) || string.IsNullOrWhiteSpace(value: subCategory))
        {
            _logger.LogWarning(
                message: "AddSubCategoryAsync called with null or empty category or subcategory. Operation skipped.");
            return Result.Failure(error: "Category or subcategory cannot be null or empty");
        }

        if (!_useAws)
        {
            _logger.LogInformation(message: "Using file-based repository for AddSubCategoryAsync");
            await _fallbackRepository.AddSubCategoryAsync(category: category
                                                          , subCategory: subCategory);
            return Result.Success();
        }

        try
        {
            // Verify table exists before attempting operations
            if (!await VerifyTableExistsAsync().ConfigureAwait(continueOnCapturedContext: false))
            {
                _logger.LogWarning(message: "DynamoDB table verification failed. Using file-based repository as fallback.");
                await _fallbackRepository.AddSubCategoryAsync(category: category
                                                              , subCategory: subCategory);
                return Result.Success();
            }

            // First check if the subcategory already exists to avoid duplicates
            var existingSubcategories
                = await GetSubCategoriesAsync(category: category).ConfigureAwait(continueOnCapturedContext: false);
            if (existingSubcategories.Value.Contains(item: subCategory))
            {
                _logger.LogInformation(
                    message: "Subcategory {SubCategory} already exists for category {Category}. Skipping add operation."
                    , subCategory
                    , category);
                return Result.Failure(error: "Subcategory already exists");
            }

            var update = new UpdateItemRequest
            {
                TableName = TableName,
                Key = new Dictionary<string, AttributeValue>
                {
                    [key: "Category"] = new() { S = category }
                }
                             ,
                AttributeUpdates = new Dictionary<string, AttributeValueUpdate>
                {
                    [key: "SubCategories"] = new()
                    {
                        Action = AttributeAction.ADD
                                                                                   ,
                        Value = new AttributeValue
                        { SS = new List<string> { subCategory } }
                    }
                }
                             ,
                ReturnValues = ReturnValue.UPDATED_NEW // Get the updated values back for verification
            };

            var response = await _dynamoDb.UpdateItemAsync(request: update).ConfigureAwait(continueOnCapturedContext: false);

            if (response == null)
            {
                _logger.LogWarning(
                    message: "DynamoDB UpdateItemAsync returned null response. Using file-based repository as fallback.");
                await _fallbackRepository.AddSubCategoryAsync(category: category
                                                              , subCategory: subCategory);
                return Result.Success();
            }

            _logger.LogInformation(message: "Successfully added subcategory {SubCategory} to category {Category} in DynamoDB"
                                   , subCategory
                                   , category);
            return Result.Success();
        }
        catch (ResourceNotFoundException)
        {
            _logger.LogWarning(message: "DynamoDB table {TableName} not found. Using file-based repository as fallback."
                               , TableName);
            await _fallbackRepository.AddSubCategoryAsync(category: category
                                                          , subCategory: subCategory);
            return Result.Success();
        }
        catch (ProvisionedThroughputExceededException ptEx)
        {
            _logger.LogWarning(exception: ptEx
                               , message: "DynamoDB provisioned throughput exceeded. Using file-based repository as fallback.");
            await _fallbackRepository.AddSubCategoryAsync(category: category
                                                          , subCategory: subCategory);
            return Result.Success();
        }
        catch (AmazonServiceException asEx)
        {
            _logger.LogError(exception: asEx
                             , message: "AWS service error accessing DynamoDB. Using file-based repository as fallback.");
            await _fallbackRepository.AddSubCategoryAsync(category: category
                                                          , subCategory: subCategory);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Unexpected error accessing DynamoDB. Using file-based repository as fallback.");
            await _fallbackRepository.AddSubCategoryAsync(category: category
                                                          , subCategory: subCategory);
            return Result.Success();
        }
    }

    public async Task<Result<bool>> RemoveSubCategoryAsync(string category
        , string subCategory)
    {
        if (string.IsNullOrWhiteSpace(value: category) || string.IsNullOrWhiteSpace(value: subCategory))
        {
            _logger.LogWarning(
                message: "RemoveSubCategoryAsync called with null or empty category or subcategory. Operation skipped.");
            return Result.Failure<bool>(error: "Category or subcategory cannot be null or empty");
        }

        if (!_useAws)
        {
            _logger.LogInformation(message: "Using file-based repository for RemoveSubCategoryAsync");
            return await _fallbackRepository.RemoveSubCategoryAsync(category: category
                                                                    , subCategory: subCategory);
        }

        try
        {
            // Verify table exists before attempting operations
            if (!await VerifyTableExistsAsync().ConfigureAwait(continueOnCapturedContext: false))
            {
                _logger.LogWarning(message: "DynamoDB table verification failed. Using file-based repository as fallback.");
                return await _fallbackRepository.RemoveSubCategoryAsync(category: category
                                                                        , subCategory: subCategory);
            }

            // First retrieve existing subcategories
            var subs = await GetSubCategoriesAsync(category: category).ConfigureAwait(continueOnCapturedContext: false);
            if (!subs.Value.Contains(item: subCategory))
            {
                _logger.LogInformation(message: "Subcategory {SubCategory} not found in category {Category}. Nothing to remove."
                                       , subCategory
                                       , category);
                return Result.Success(value: false);
            }

            var update = new UpdateItemRequest
            {
                TableName = TableName,
                Key = new Dictionary<string, AttributeValue>
                {
                    [key: "Category"] = new() { S = category }
                }
                             ,
                AttributeUpdates = new Dictionary<string, AttributeValueUpdate>
                {
                    [key: "SubCategories"] = new()
                    {
                        Action = AttributeAction.DELETE
                                                                                   ,
                        Value = new AttributeValue
                        { SS = new List<string> { subCategory } }
                    }
                }
                             ,
                ReturnValues = ReturnValue.UPDATED_NEW // Get the updated values back for verification
            };

            var response = await _dynamoDb.UpdateItemAsync(request: update).ConfigureAwait(continueOnCapturedContext: false);

            if (response == null)
            {
                _logger.LogWarning(
                    message: "DynamoDB UpdateItemAsync returned null response. Using file-based repository as fallback.");
                return await _fallbackRepository.RemoveSubCategoryAsync(category: category
                                                                        , subCategory: subCategory);
            }

            _logger.LogInformation(message: "Successfully removed subcategory {SubCategory} from category {Category} in DynamoDB"
                                   , subCategory
                                   , category);
            return Result.Success(value: true);
        }
        catch (ResourceNotFoundException)
        {
            _logger.LogWarning(message: "DynamoDB table {TableName} not found. Using file-based repository as fallback."
                               , TableName);
            return await _fallbackRepository.RemoveSubCategoryAsync(category: category
                                                                    , subCategory: subCategory);
        }
        catch (ProvisionedThroughputExceededException ptEx)
        {
            _logger.LogWarning(exception: ptEx
                               , message: "DynamoDB provisioned throughput exceeded. Using file-based repository as fallback.");
            return await _fallbackRepository.RemoveSubCategoryAsync(category: category
                                                                    , subCategory: subCategory);
        }
        catch (AmazonServiceException asEx)
        {
            _logger.LogError(exception: asEx
                             , message: "AWS service error accessing DynamoDB. Using file-based repository as fallback.");
            return await _fallbackRepository.RemoveSubCategoryAsync(category: category
                                                                    , subCategory: subCategory);
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Unexpected error accessing DynamoDB. Using file-based repository as fallback.");
            return await _fallbackRepository.RemoveSubCategoryAsync(category: category
                                                                    , subCategory: subCategory);
        }
    }

    /// <summary>
    ///     Verifies that the DynamoDB table exists and is accessible
    /// </summary>
    private async Task<bool> VerifyTableExistsAsync()
    {
        if (!_useAws || _tableVerified) return _useAws && _tableVerified;

        try
        {
            var request = new DescribeTableRequest
            {
                TableName = TableName
            };

            var response = await _dynamoDb.DescribeTableAsync(request: request).ConfigureAwait(continueOnCapturedContext: false);
            _tableVerified = response.Table != null;

            if (_tableVerified)
                _logger.LogInformation(message: "Successfully verified DynamoDB table {TableName} exists"
                                       , TableName);
            else
                _logger.LogWarning(message: "DynamoDB table {TableName} not found"
                                   , TableName);

            return _tableVerified;
        }
        catch (ResourceNotFoundException)
        {
            _logger.LogWarning(message: "DynamoDB table {TableName} does not exist"
                               , TableName);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error verifying DynamoDB table {TableName}"
                             , TableName);
            return false;
        }
    }
}