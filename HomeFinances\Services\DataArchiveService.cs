using Azure.Storage.Blobs;
using System.Globalization;

namespace HomeFinances.Services;

public class DataArchiveService
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly string _containerName = "data";

    public DataArchiveService(BlobServiceClient blobServiceClient)
    {
        _blobServiceClient = blobServiceClient;
    }

    public async Task ArchiveFutureTransactions()
    {
        var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
        var sourceBlobName = "FutureTransactions.csv";
        var date = DateTime.Now.ToString("yyyy-MM-dd");
        var archiveBlobName = $"archive/FutureTransactions_{date}.csv";

        var sourceBlobClient = containerClient.GetBlobClient(sourceBlobName);
        var archiveBlobClient = containerClient.GetBlobClient(archiveBlobName);

        // Copy the current blob to archive
        await archiveBlobClient.StartCopyFromUriAsync(sourceBlobClient.Uri);

        // Create empty file in the original location
        using var stream = new MemoryStream();
        await sourceBlobClient.UploadAsync(stream, overwrite: true);
    }
} 