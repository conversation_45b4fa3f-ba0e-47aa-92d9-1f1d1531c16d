﻿using HomeFinances.Models;
using HomeFinances.Store.FutureTransactions;
using HomeFinances.Services;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Fluxor;
using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace HomeFinances.Components.Dialogs
{
    public partial class AddFutureTransaction : IDisposable
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Inject] private IState<FutureTransactionState> FutureTransactionState { get; set; } = default!;
        [Inject] private IDispatcher Dispatcher { get; set; } = default!;
        [Inject] private ISnackbar Snackbar { get; set; } = default!;
        [Inject] private FutureTransactionService FutureTransactionService { get; set; } = default!;
        [Inject] private NavigationManager NavigationManager { get; set; } = default!;
        [Inject] private ILogger<AddFutureTransaction> _logger { get; set; }

        private MudForm form;
        private bool isOutgo = true;
        private bool _isSubmitting = false;

        private FutureBankTransaction _futureBankTransaction = new FutureBankTransaction
        {
            TransactionDate = DateTime.Today,
            Id = Guid.Empty
        };

        protected override void OnInitialized()
        {
            FutureTransactionState.StateChanged += OnStateChanged;
        }

        private void OnStateChanged(object sender, EventArgs e)
        {
            _logger.LogInformation("State changed: Loading status = {Status}", FutureTransactionState.Value.Loading.Status);
            if (_isSubmitting && FutureTransactionState.Value.Loading.Status == LoadingStatusEnum.Loaded)
            {
                _logger.LogInformation("Transaction loaded, closing dialog");
                _isSubmitting = false;
                Snackbar.Add("Transaction added successfully", Severity.Success);
                MudDialog.Close(DialogResult.Ok(_futureBankTransaction));
            }
        }

        private void Cancel()
        {
            MudDialog.Cancel();
        }

        public async Task Submit()
        {
            try
            {
                if (form.IsValid)
                {
                    _logger.LogWarning("=== START: Add Transaction Flow ===");
                    _logger.LogWarning("Submit button clicked - Starting transaction submission");
                    _logger.LogWarning("Initial transaction state: Id={Id}, Description={Description}, Amount={Amount:C}, Date={Date:d}", 
                        _futureBankTransaction.Id,
                        _futureBankTransaction.Description,
                        _futureBankTransaction.TransactionAmount,
                        _futureBankTransaction.TransactionDate);

                    if (isOutgo && _futureBankTransaction.TransactionAmount > 0)
                    {
                        _futureBankTransaction.TransactionAmount = -Math.Abs(_futureBankTransaction.TransactionAmount);
                        _logger.LogWarning("Adjusted amount for outgo: {Amount:C}", _futureBankTransaction.TransactionAmount);
                    }

                    _isSubmitting = true;
                    _logger.LogWarning("Dispatching UpdateFutureTransactionAction");
                    Dispatcher.Dispatch(new FutureTransactionActions.UpdateFutureTransactionAction(_futureBankTransaction));
                    
                    await Task.Delay(100);
                    _logger.LogWarning("=== END: Add Transaction Flow ===");
                    MudDialog.Close(DialogResult.Ok(true));
                }
                else
                {
                    _logger.LogWarning("Form validation failed - Cannot submit transaction");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error submitting transaction: {Error}", ex.Message);
                _isSubmitting = false;
            }
        }

        public void Dispose()
        {
            FutureTransactionState.StateChanged -= OnStateChanged;
        }
    }
}
