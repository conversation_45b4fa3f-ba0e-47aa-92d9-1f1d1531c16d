﻿@using System.IO
@using System.Globalization
@using CsvHelper
@using CsvHelper.Configuration
@using HomeFinances.Models.Mapping
@using HomeFinances.Models.Mapping.HomeFinances.Models.Mapping
@using Microsoft.AspNetCore.Components.Forms

<MudFileUpload T="IBrowserFile" Accept=".csv" FilesChanged="UploadFile" MaximumFileCount="1">
    <ButtonTemplate>
        <MudButton HtmlTag="label"
                   Variant="Variant.Filled"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.CloudUpload"
                   for="@context.Id">
            Upload Beehive Transaction CSV File
        </MudButton>
    </ButtonTemplate>
</MudFileUpload>

@if (!string.IsNullOrEmpty(uploadMessage))
{
    <MudAlert Severity="@(isSuccess ? Severity.Success : Severity.Error)"
              Color="@(isSuccess ? Color.Success : Color.Error)">
        @uploadMessage
    </MudAlert>
}

@code {
    private string uploadMessage;
    private bool isSuccess;

    private async Task UploadFile(IBrowserFile file)
    {
        uploadMessage = null;
        isSuccess = false;

        if (file != null)
        {
            try
            {
                var maxAllowedSize = 1024 * 1024 * 10; // 10 MB limit
                if (file.Size > maxAllowedSize)
                {
                    uploadMessage = "File size exceeds the maximum allowed limit.";
                    return;
                }

                var buffer = new MemoryStream();
                await file.OpenReadStream(maxAllowedSize).CopyToAsync(buffer);
                buffer.Seek(0, SeekOrigin.Begin); // Reset buffer position

                var config = new CsvConfiguration(CultureInfo.InvariantCulture)
                    {
                        HasHeaderRecord = true,
                    };

                using var reader = new StreamReader(buffer);
                using var csv = new CsvReader(reader, config);

                csv.Context.RegisterClassMap<BeeHiveTransactionMap>();
                var records = csv.GetRecords<BeeHiveTransaction>();

                if (records.Any())
                {
                    // Ensure directory exists
                    var directory = Path.Combine("Data", "Uploads", "BeehiveTransactions");
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    var savePath = Path.Combine(directory, $"BeehiveTransactions-{DateTime.Now:yyyy-MM-dd-HH-mm}.csv");

                    using (var fileStream = File.Create(savePath))
                    {
                        buffer.Seek(0, SeekOrigin.Begin); // Reset buffer position again for copy
                        await buffer.CopyToAsync(fileStream);
                    }

                    uploadMessage = "File uploaded and validated successfully.";
                    isSuccess = true;
                }
                else
                {
                    uploadMessage = "No valid transactions found.";
                }
            }
            catch (Exception ex)
            {
                uploadMessage = $"Error uploading file: {ex.Message}";
            }
        }
    }
}
