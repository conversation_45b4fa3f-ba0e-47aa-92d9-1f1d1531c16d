using Fluxor;
using HomeFinances.Components;
using HomeFinances.Services;
using HomeFinances.Store.Account;
using HomeFinances.Store.BankTransactions;
using HomeFinances.Store.BeeHiveTransactions;
using HomeFinances.Store.Downloads;
using HomeFinances.Store.FutureTransactions;
using HomeFinances.Store.GeneratedImages;
using HomeFinances.Store.IgnoredTransactions;
using HomeFinances.Store.RecurringTransactions;
using Microsoft.Extensions.FileProviders;
using MudBlazor.Services;

var builder = WebApplication.CreateBuilder(args: args);
// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();
builder.Logging.SetMinimumLevel(level: LogLevel.Warning);

// Add services to the container.
builder.Services.AddRazorComponents()
   .AddInteractiveServerComponents();

builder.Services.AddFluxor(options =>
{
    options.ScanAssemblies(typeof(Program).Assembly);
    options.UseReduxDevTools();
});

builder.Services.AddMudServices();
builder.Services.AddAppServices();
builder.Services.AddHttpClient();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler(errorHandlingPath: "/Error"
                            , createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
Directory.CreateDirectory(path: Path.Combine(Path.Combine(path1: app.Environment.ContentRootPath
                                                          , path2: "Data"
                                                          , path3: "Uploads"
                                                          , path4: "Receipts")));
app.UseStaticFiles();
app.UseStaticFiles(options: new StaticFileOptions
                            {
                                FileProvider = new PhysicalFileProvider(
                                    root: Path.Combine(path1: app.Environment.ContentRootPath
                                                       , path2: "Data"
                                                       , path3: "Uploads"
                                                       , path4: "Receipts"))
                                , RequestPath = "/Data/Uploads/Receipts"
                            });
app.UseAntiforgery();

app.MapRazorComponents<App>()
   .AddInteractiveServerRenderMode();

app.Run();