Codex Context Summary – May 4, 2025 (Detailed Version)

        1. Architectural Goal
        Migrate all file/blob direct storage to a clean, testable, cloud/serverless-friendly Repository abstraction
    using C# interfaces for each major domain.
        Make it easy to switch storage technology (AWS, Azure, File, etc.) via DI and config, supporting serverless or
    container platforms.

        2. Implementation Changes Completed
        - Category storage is now abstracted as ICategoryRepository.
        - A production AWS DynamoDB implementation AwsDynamoCategoryRepository is provided.
        - Table schema: { PK: Category, SubCategories: [set of strings] }
        - Dependency injection is configured to inject this repository.
        - CategoryService is now refactored to use ICategoryRepository for all access.
        - XUnit tests for AwsDynamoCategoryRepository have been created (integration-style, for DynamoDB local or AWS
    testing).
        - A bash script is available to consolidate and move all repository and existing tests into HomeFinances.Tests,
    cleaning up extra projects/folders.

        3. Project Status
        Started refactoring Categories with repository pattern. Ready to repeat for all other major domain objects.
        Test files are staged to be moved into HomeFinances.Tests.
        After migration, all test coverage will be run from one consolidated project.
        Additional repositories (transactions, mappings, etc.) will be migrated using the same interface-driven style,
    and verified with unit/integration tests.

        4. Next Steps/Usage Guidance
        Continue tackling each storage area (mappings, transactions, loans, accounts) using the steps below.
        Use provided scripts to keep the repo’s .sln and folder structure clean/reusable.
        Revisit this summary for interface names and design plan if the setup session is interrupted/restarted.

        5. Planned Repository Interfaces (for all project entities)
        All repositories should provide async CRUD for their entities, support DI, and allow for both cloud and local
    implementations:
            - ICategoryRepository — Category and subcategory CRUD
            - ICategoryMappingRepository — Description-to-category/subcategory mapping CRUD
            - IBankTransactionRepository — Per-account bank transaction CRUD
            - IFutureTransactionRepository — Per-account future/scheduled transaction CRUD
            - IRecurringTransactionRepository — Per-account recurring transaction CRUD
            - ILoanRepository — Per-account loan CRUD
            - ILoanTransactionRepository — Per-loan payment/transaction CRUD
            - IAccountRepository — Account record CRUD
            - IIgnoredTransactionRepository — Ignored/bypassed transactions
            - IImageGenerationRepository — For standardized image/blob/document storage


	Implement each of ICategoryRepository, ICategoryMappingRepository,IBankTransactionRepository in their own DDD/Microservice style project.
	Each project will have a Web folder if relevant for Blazor, A Core folder that will hold the relevant models and interfaces. Relevant interfaces for services.
	An infrastructure folder that holds the concrete implementations of the services and repositories for the Micro Service.

	The Current Project in HomeFinances should be turned into a base project to run the services from the other projects to implement the needed functionality. Please delete 
everything related to loans. Please move all code an logic to their appropriate DDD project. 
 
        6. Step-by-step Implementation Cycle (for each repository)
        

	1. Define the interface (e.g. ICategoryRepository) with all required async CRUD.
        2. Write a concrete AWS DynamoDB (or file/blob) class, e.g. AwsDynamoCategoryRepository.
        3. Write XUnit integration/unit tests for the implementation (using e.g. DynamoDB Local).
        4. Refactor dependent services to use the interface via dependency injection.

        7. Environment File Creation Quirk (and the 'touch' workaround)
        In this Codex/WSL environment, you must use the `touch` shell command to create a new file before writing code
    or scripts to it.
        If you attempt to programmatically write directly to a non-existent file, it may not appear in your tree—even
    though the operation claims to succeed.
        Always touch the file first (e.g. `touch Scripts/run-category-repo-tests.sh`) before updating code or copying
    content into it.

        8. Scripts created and referenced in this workflow
        - consolidate-tests.sh — Automates moving all test files into HomeFinances.Tests and updating/removing
    solution/project references.
        - run-category-repo-tests.sh — Automates test project generation, linking, and running of repository XUnit
    tests.
