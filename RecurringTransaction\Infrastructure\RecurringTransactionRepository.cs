using System.Text.Json;
using Azure.Storage.Blobs;
using HomeFinances.RecurringTransaction.Core;
using Microsoft.Extensions.Logging;

namespace HomeFinances.RecurringTransaction.Infrastructure;

/// <summary>
///     Blob-based implementation of IRecurringTransactionRepository using JSON storage.
/// </summary>
public class RecurringTransactionRepository : IRecurringTransactionRepository
{
    private const string ContainerName = "data";
    private const string BlobName = "RecurringTransactions.json";
    private readonly BlobServiceClient _blobServiceClient;
    private readonly ILogger<RecurringTransactionRepository> _logger;

    public RecurringTransactionRepository(BlobServiceClient blobServiceClient
        , ILogger<RecurringTransactionRepository> logger)
    {
        _blobServiceClient = blobServiceClient;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<global::RecurringTransaction>> GetAllAsync()
    {
        var container = _blobServiceClient.GetBlobContainerClient(blobContainerName: ContainerName);
        await container.CreateIfNotExistsAsync().ConfigureAwait(continueOnCapturedContext: false);
        var blob = container.GetBlobClient(blobName: BlobName);
        if (!await blob.ExistsAsync().ConfigureAwait(continueOnCapturedContext: false))
            return new List<global::RecurringTransaction>();
        using var stream = new MemoryStream();
        await blob.DownloadToAsync(destination: stream).ConfigureAwait(continueOnCapturedContext: false);
        stream.Position = 0;
        var items = await JsonSerializer.DeserializeAsync<List<global::RecurringTransaction>>(utf8Json: stream)
           .ConfigureAwait(continueOnCapturedContext: false);
        return items ?? new List<global::RecurringTransaction>();
    }

    /// <inheritdoc />
    public async Task<global::RecurringTransaction?> GetByIdAsync(Guid id)
    {
        var all = await GetAllAsync().ConfigureAwait(continueOnCapturedContext: false);
        return all.FirstOrDefault(predicate: t => t.Id == id);
    }

    /// <inheritdoc />
    public async Task AddOrUpdateAsync(global::RecurringTransaction transaction)
    {
        var list = (await GetAllAsync().ConfigureAwait(continueOnCapturedContext: false)).ToList();
        var index = list.FindIndex(match: t => t.Id == transaction.Id);
        if (index >= 0)
        {
            list[index: index] = transaction;
        }
        else
        {
            if (transaction.Id == Guid.Empty) transaction.Id = Guid.NewGuid();
            list.Add(item: transaction);
        }

        await WriteAllAsync(transactions: list).ConfigureAwait(continueOnCapturedContext: false);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(Guid id)
    {
        var list = (await GetAllAsync().ConfigureAwait(continueOnCapturedContext: false)).ToList();
        var updated = list.Where(predicate: t => t.Id != id).ToList();
        await WriteAllAsync(transactions: updated).ConfigureAwait(continueOnCapturedContext: false);
    }

    private async Task WriteAllAsync(IEnumerable<global::RecurringTransaction> transactions)
    {
        var container = _blobServiceClient.GetBlobContainerClient(blobContainerName: ContainerName);
        await container.CreateIfNotExistsAsync().ConfigureAwait(continueOnCapturedContext: false);
        var blob = container.GetBlobClient(blobName: BlobName);
        using var mem = new MemoryStream();
        await JsonSerializer.SerializeAsync(utf8Json: mem
                                            , value: transactions
                                            , options: new JsonSerializerOptions { WriteIndented = true })
           .ConfigureAwait(continueOnCapturedContext: false);
        mem.Position = 0;
        await blob.UploadAsync(content: mem
                               , overwrite: true).ConfigureAwait(continueOnCapturedContext: false);
    }
}