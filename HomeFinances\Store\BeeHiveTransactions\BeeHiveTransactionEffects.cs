﻿using Fluxor;
using HomeFinances.Services;
using static HomeFinances.Store.BankTransactions.BankTransactionActions;

namespace HomeFinances.Store.BeeHiveTransactions
{
    public class BeeHiveTransactionEffects
    {
        private readonly TransactionReadWriteService _transactionReadWriteService;
        private readonly CategoryService _categoryService;
        private readonly FutureTransactionService _futureTransactionService;

        public BeeHiveTransactionEffects(TransactionReadWriteService transactionReadWriteService, CategoryService categoryService, FutureTransactionService futureTransactionService)
        {
            _transactionReadWriteService = transactionReadWriteService;
            _categoryService = categoryService;
            _futureTransactionService = futureTransactionService;
        }

        [EffectMethod]
        public async Task HandleLoadBeehiveTransactionsAction(BeeHiveTransactionActions.LoadBeehiveTransactionsAction action, IDispatcher dispatcher)
        {
            try
            {
                var transactions = await _transactionReadWriteService.ReadBeehiveTransactionsFromFolder("Data/Uploads/BeehiveTransactions");
                foreach (var transaction in transactions)
                {
                    //transaction.Taxonomy = await _categoryMappingService.GetCategoryAndSubCategory(transaction.Description);
                }
                dispatcher.Dispatch(new BeeHiveTransactionActions.LoadBeehiveTransactionsSuccessAction(transactions));
                dispatcher.Dispatch(new MergeBeehiveTransactionsAction(transactions));
            }
            catch (Exception ex)
            {
                dispatcher.Dispatch(new BeeHiveTransactionActions.LoadBeehiveTransactionsFailedAction(ex.Message));
            }
        }
    }
}
