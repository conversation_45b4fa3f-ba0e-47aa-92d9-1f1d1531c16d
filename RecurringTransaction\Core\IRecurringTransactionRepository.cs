namespace HomeFinances.RecurringTransaction.Core;

/// <summary>
///     Repository abstraction for recurring transactions.
/// </summary>
public interface IRecurringTransactionRepository
{
    /// <summary>
    ///     Gets all recurring transactions.
    /// </summary>
    Task<IEnumerable<global::RecurringTransaction>> GetAllAsync();

    /// <summary>
    ///     Gets a recurring transaction by its identifier.
    /// </summary>
    Task<global::RecurringTransaction> GetByIdAsync(Guid id);

    /// <summary>
    ///     Adds or updates a recurring transaction.
    /// </summary>
    Task AddOrUpdateAsync(global::RecurringTransaction transaction);

    /// <summary>
    ///     Deletes a recurring transaction by its identifier.
    /// </summary>
    Task DeleteAsync(Guid id);
}