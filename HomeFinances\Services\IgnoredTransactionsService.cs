﻿using Azure.Storage.Blobs;
using HomeFinances.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace HomeFinances.Services
{
    public class IgnoredTransactionsService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly string _blobContainerName = "data";
        private readonly string _blobName = "IgnoredTransactions.json";

        public IgnoredTransactionsService(BlobServiceClient blobServiceClient)
        {
            _blobServiceClient = blobServiceClient;
        }

        public async Task<List<PossiblyMissingTransactionViewModel>> LoadIgnoredTransactionsAsync()
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(_blobContainerName);
                var blobClient = containerClient.GetBlobClient(_blobName);

                if (await blobClient.ExistsAsync())
                {
                    var response = await blobClient.DownloadAsync();
                    using (var stream = response.Value.Content)
                    {
                        return await JsonSerializer.DeserializeAsync<List<PossiblyMissingTransactionViewModel>>(stream) ?? new List<PossiblyMissingTransactionViewModel>();
                    }
                }
                else
                {
                    Console.WriteLine("Blob does not exist, returning new empty list.");
                    return new List<PossiblyMissingTransactionViewModel>();
                }
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"JSON parsing error: {ex.Message}");
                return new List<PossiblyMissingTransactionViewModel>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while reading the blob: {ex.Message}");
                throw;
            }
        }

        public async Task SaveIgnoredTransactionsAsync(List<PossiblyMissingTransactionViewModel> transactions)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(_blobContainerName);
                var blobClient = containerClient.GetBlobClient(_blobName);

                using (var memoryStream = new MemoryStream())
                {
                    await JsonSerializer.SerializeAsync(memoryStream, transactions, new JsonSerializerOptions { WriteIndented = true });
                    memoryStream.Position = 0; // Reset stream position to beginning after writing
                    await blobClient.UploadAsync(memoryStream, overwrite: true);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while writing to the blob: {ex.Message}");
                throw; // Re-throw the exception to handle it further up the call stack
            }
        }

        public async Task IgnoreTransaction(PossiblyMissingTransactionViewModel transaction)
        {
            var transactions = await LoadIgnoredTransactionsAsync();

            // Add the new transaction to the list if it is not already there, identified by ID
            if (!transactions.Exists(t => t.Id == transaction.Id))
            {
                transactions.Add(transaction);
                await SaveIgnoredTransactionsAsync(transactions);
            }
        }
    }
}
