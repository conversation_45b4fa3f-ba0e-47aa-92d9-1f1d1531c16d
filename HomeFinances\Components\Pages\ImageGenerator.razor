﻿@page "/generate-image"
@rendermode InteractiveServer
@using HomeFinances.Store.GeneratedImages
@using Fluxor
@using HomeFinances.Models
@using HomeFinances.Models.ChatGPT
@using HomeFinances.Services
@using HomeFinances.Repositories
@using Microsoft.AspNetCore.Components
@using Microsoft.JSInterop
@inject IState<GeneratedImagesState> GeneratedImagesState
@inject IDispatcher Dispatcher
@inject ILogger<ImageGenerator> Lo<PERSON>
@implements IDisposable
@inject IJSRuntime JSRuntime

<style>
    .sortable-item {
        cursor: grab;
    }
    .sortable-item:active {
        cursor: grabbing;
    }
</style>

<MudThemeProvider />
<MudDialogProvider />
<MudSnackbarProvider />
<Fluxor.Blazor.Web.StoreInitializer />

<MudContainer>
    <div class="ctrl-objects">
    <MudText Typo="Typo.h4">Generate Image from Description</MudText>
    <MudTextField @bind-Value="description" Label="Description"/>
    <MudButton OnClick="AddImage" Disabled="isGenerating">Generate</MudButton>
    <MudProgressCircular Indeterminate="true" Style="@ProgressStyle"/>
    <MudText Typo="Typo.body1" Color="Color.Error" Style="@ErrorStyle">@errorMessage</MudText>

    <MudText Typo="Typo.h5" Class="mt-4">Generated Images</MudText>
    @* <MudSwitch @bind-Checked="isTileView" Label="Tile View" Color="Color.Primary" Class="mb-4"/> *@
    </div>
    @if (true || isTileView)
    {
        <div class="ctrl-objects">
        <MudSwitch @bind-Value="showRegenButtons" Label="Show Regenerate Image Buttons" Color="Color.Primary" Class="mb-4"/>
        <MudSwitch @bind-Value="showDeleteButtons" Label="Show Delete Image Buttons" Color="Color.Primary" Class="mb-4"/>
        </div>
        <MudGrid id="grid-container">
            @foreach (var image in sortedImages)
            {
                <div class="sortable-item">
                <MudItem xs="12" sm="6" md="4" lg="3">
                    <MudPaper Class="image-tile-not-working" Style="@imageTileStyle">
                        <MudImage Src="@image.ImageUrl" Width="200"/>
                        <MudText Typo="Typo.h6">@image.Prompt</MudText>
                        @if (showRegenButtons)
                        {
                            <MudButton OnClick="() => RegenerateImage(image.Prompt)" Color="Color.Primary">Regenerate</MudButton>
                        }
                        @if (showDeleteButtons)
                        {
                            <MudButton OnClick="() => DeleteImage(image.PartitionKey, image.Prompt)" Color="Color.Error">Delete</MudButton>
                        }
                    </MudPaper>
                </MudItem>
                </div>
            }
        </MudGrid>
    }
    else
    {
        <MudTable Items="@sortedImages" Striped="true">
            <HeaderContent>
                <MudTh>Item</MudTh>
                <MudTh>Image</MudTh>
                <MudTh></MudTh>
                <MudTh></MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd>@context.Prompt</MudTd>
                <MudTd><MudImage Src="@context.ImageUrl" Width="150"/></MudTd>
                <MudTd>
                    @if (regeneratingImages.ContainsKey(context.Prompt) && regeneratingImages[context.Prompt])
                    {
                        <MudProgressCircular Indeterminate="true" Size="Size.Small"/>
                    }
                    else
                    {
                        <MudButton OnClick="() => RegenerateImage(context.Prompt)" Color="Color.Primary">Regenerate</MudButton>
                    }
                </MudTd>
                <MudTd><MudButton OnClick="() => DeleteImage(context.PartitionKey, context.Prompt)" Color="Color.Error">Delete</MudButton></MudTd>
            </RowTemplate>
        </MudTable>
    }
</MudContainer>

@code {
    private string description;
    private string? errorMessage;
    private bool isTileView = true;
    private bool showRegenButtons = false;
    private bool showDeleteButtons = false;
    private Dictionary<string, bool> regeneratingImages = new Dictionary<string, bool>();
    private string imageTileStyle = "width: 300px; height: 300px; display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 16px; border: 3px solid #ccc; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);";
    private List<ImageGenerationEntity> images = new();

    private bool isGenerating => GeneratedImagesState.Value.IsLoading;
    private List<GeneratedImage> sortedImages => GeneratedImagesState.Value.Images.OrderBy(img => img.Prompt).ToList();

    private string ProgressStyle => isGenerating ? "visibility: visible;" : "visibility: hidden;";
    private string ErrorStyle => !string.IsNullOrEmpty(GeneratedImagesState.Value.ErrorMessage) ? "visibility: visible;" : "visibility: hidden;";

    private DotNetObjectReference<ImageGenerator> objRef;
    private IJSObjectReference jsModule;

    [Inject]
    private ImageGenerationRepository ImageGenerationRepository { get; set; } = default!;

    [Inject]
    private ChatGptApiService ChatGptApiService { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        await LoadImages();
        GeneratedImagesState.StateChanged += OnStateHasChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./scripts/dragDrop.js");
            objRef = DotNetObjectReference.Create(this);
            await jsModule.InvokeVoidAsync("makeSortable", "grid-container", objRef);
        }
    }

    [JSInvokable]
    public async Task UpdateOrder(int oldIndex, int newIndex)
    {
        var item = sortedImages.ElementAt(oldIndex);
        sortedImages.RemoveAt(oldIndex);
        sortedImages.Insert(newIndex, item);
    }

    private void OnStateHasChanged(object? sender, EventArgs e)
    {
        StateHasChanged();
    }

    public void Dispose()
    {
        GeneratedImagesState.StateChanged -= OnStateHasChanged;
        objRef?.Dispose();
        jsModule?.DisposeAsync();
    }

    private void AddImage()
    {
        errorMessage = string.Empty;
        Dispatcher.Dispatch(new GeneratedImagesActions.AddGeneratedImageAction(description, 0.0M));
    }

    private void DeleteImage(string partitionKey, string rowKey)
    {
        Dispatcher.Dispatch(new GeneratedImagesActions.DeleteGeneratedImageAction(partitionKey, rowKey));
    }

    private void RegenerateImage(string imageUrl)
    {
        Dispatcher.Dispatch(new GeneratedImagesActions.UpdateGeneratedImageAction(imageUrl, 0.0M));
    }

    private async Task LoadImages()
    {
        try
        {
           var imageRepoResult = await ImageGenerationRepository.GetAllImageGenerationsAsync();
        images = imageRepoResult.Value;
    }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
        }
    }
}
