namespace HomeFinances.FutureTransaction.Infrastructure
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using Azure.Storage.Blobs;
    using CsvHelper;
    using HomeFinances.FutureTransaction.Core;
    using HomeFinances.Models;
    using HomeFinances.Models.Mapping;
    using Microsoft.Extensions.Logging;

    /// <summary>
    /// Blob-based implementation of IFutureTransactionRepository using CSV storage.
    /// </summary>
    public class FutureTransactionRepository : IFutureTransactionRepository
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly ILogger<FutureTransactionRepository> _logger;
        private const string ContainerName = "data";
        private const string BlobName = "FutureTransactions.csv";
        private bool _initialized;

        public FutureTransactionRepository(BlobServiceClient blobServiceClient, ILogger<FutureTransactionRepository> logger)
        {
            _blobServiceClient = blobServiceClient;
            _logger = logger;
        }

        private async Task EnsureInitializedAsync()
        {
            if (_initialized) return;
            var container = _blobServiceClient.GetBlobContainerClient(ContainerName);
            await container.CreateIfNotExistsAsync().ConfigureAwait(false);
            var blob = container.GetBlobClient(BlobName);
            if (!await blob.ExistsAsync().ConfigureAwait(false))
            {
                // create empty CSV with header
                using var mem = new MemoryStream();
                using var writer = new StreamWriter(mem);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
                csv.Context.RegisterClassMap<FutureTransactionMap>();
                csv.WriteRecords(new List<FutureBankTransaction>());
                await writer.FlushAsync().ConfigureAwait(false);
                mem.Position = 0;
                await blob.UploadAsync(mem, overwrite: true).ConfigureAwait(false);
            }
            _initialized = true;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<FutureBankTransaction>> GetAllAsync()
        {
            await EnsureInitializedAsync().ConfigureAwait(false);
            var container = _blobServiceClient.GetBlobContainerClient(ContainerName);
            var blob = container.GetBlobClient(BlobName);
            using var mem = new MemoryStream();
            await blob.DownloadToAsync(mem).ConfigureAwait(false);
            mem.Position = 0;
            using var reader = new StreamReader(mem);
            using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
            csv.Context.RegisterClassMap<FutureTransactionMap>();
            var records = csv.GetRecords<FutureBankTransaction>().ToList();
            return records;
        }

        /// <inheritdoc />
        public async Task<FutureBankTransaction?> GetByIdAsync(Guid id)
        {
            var all = await GetAllAsync().ConfigureAwait(false);
            return all.FirstOrDefault(t => t.Id == id);
        }

        /// <inheritdoc />
        public async Task AddOrUpdateAsync(FutureBankTransaction transaction)
        {
            var list = (await GetAllAsync().ConfigureAwait(false)).ToList();
            var index = list.FindIndex(t => t.Id == transaction.Id);
            if (index >= 0)
            {
                list[index] = transaction;
            }
            else
            {
                if (transaction.Id == Guid.Empty)
                {
                    transaction.Id = Guid.NewGuid();
                }
                list.Add(transaction);
            }
            await WriteAllAsync(list).ConfigureAwait(false);
        }

        /// <inheritdoc />
        public async Task DeleteAsync(Guid id)
        {
            var list = (await GetAllAsync().ConfigureAwait(false)).ToList();
            var updated = list.Where(t => t.Id != id).ToList();
            await WriteAllAsync(updated).ConfigureAwait(false);
        }

        private async Task WriteAllAsync(IEnumerable<FutureBankTransaction> transactions)
        {
            var container = _blobServiceClient.GetBlobContainerClient(ContainerName);
            var blob = container.GetBlobClient(BlobName);
            using var mem = new MemoryStream();
            using var writer = new StreamWriter(mem);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
            csv.Context.RegisterClassMap<FutureTransactionMap>();
            csv.WriteRecords(transactions);
            await writer.FlushAsync().ConfigureAwait(false);
            mem.Position = 0;
            await blob.UploadAsync(mem, overwrite: true).ConfigureAwait(false);
        }
    }
}