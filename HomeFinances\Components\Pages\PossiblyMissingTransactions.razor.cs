﻿using Fluxor;
using HomeFinances.Models;
using HomeFinances.Services;
using HomeFinances.Store.BankTransactions;
using HomeFinances.Store.FutureTransactions;
using HomeFinances.Store.IgnoredTransactions;
using HomeFinances.Store.RecurringTransactions;
using Microsoft.AspNetCore.Components;
using static HomeFinances.Store.IgnoredTransactions.IgnoredTransactionActions;

namespace HomeFinances.Components.Pages;

public partial class PossiblyMissingTransactions : ComponentBase
{
    private bool IsAllSelected;

    private List<PossiblyMissingTransactionViewModel> PossiblyMissingRecurringTransactions = new();
    [Inject] private IState<RecurringTransactionsState> RecurringTransactionsState { get; set; }
    [Inject] private IState<BankTransactionState> BankTransactionState { get; set; }
    [Inject] private IState<FutureTransactionState> FutureTransactionState { get; set; }
    [Inject] private IState<IgnoredTransactionsState> IgnoredTransactionsState { get; set; }
    [Inject] private IDispatcher Dispatcher { get; set; }
    [Inject] private ILogger<PossiblyMissingTransactions> _logger { get; set; }
    [Inject] private FutureTransactionService FutureTransactionService { get; set; }

    private bool IsLoading =>
        (RecurringTransactionsState.Value?.Loading?.Status ?? LoadingStatusEnum.NotLoaded) == LoadingStatusEnum.Loading ||
        (BankTransactionState.Value?.Loading?.Status ?? LoadingStatusEnum.NotLoaded) == LoadingStatusEnum.Loading ||
        (FutureTransactionState.Value?.Loading?.Status ?? LoadingStatusEnum.NotLoaded) == LoadingStatusEnum.Loading;

    private bool DataLoaded => RecurringTransactionsState.Value?.Loading?.Status == LoadingStatusEnum.Loaded &&
                               BankTransactionState.Value?.Loading?.Status == LoadingStatusEnum.Loaded &&
                               FutureTransactionState.Value?.Loading?.Status == LoadingStatusEnum.Loaded &&
                               IgnoredTransactionsState.Value?.Loading.Status == LoadingStatusEnum.Loaded;

    private List<PossiblyMissingTransactionViewModel> IgnoredTransactions =>
        IgnoredTransactionsState?.Value?.IgnoredTransactions ?? new List<PossiblyMissingTransactionViewModel>();

    protected override void OnInitialized()
    {
        base.OnInitialized();
        RecurringTransactionsState.StateChanged += HandleStateChange;
        BankTransactionState.StateChanged += HandleStateChange;
        FutureTransactionState.StateChanged += HandleStateChange;
        IgnoredTransactionsState.StateChanged += HandleStateChange;

        LoadData();
    }

    private void HandleStateChange(object? sender
        , EventArgs e)
    {
        LoadData();
        InvokeAsync(workItem: StateHasChanged);
    }

    private void LoadData()
    {
        if (RecurringTransactionsState.Value?.Loading?.Status == LoadingStatusEnum.NotLoaded)
            Dispatcher.Dispatch(action: new RecurringTransactionActions.LoadRecurringTransactionsAction());
        if (BankTransactionState.Value?.Loading?.Status == LoadingStatusEnum.NotLoaded)
            Dispatcher.Dispatch(action: new BankTransactionActions.LoadTransactionsAction());
        if (FutureTransactionState.Value?.Loading?.Status == LoadingStatusEnum.NotLoaded)
            Dispatcher.Dispatch(action: new FutureTransactionActions.LoadFutureTransactionsAction());
        if (IgnoredTransactionsState.Value?.Loading?.Status == LoadingStatusEnum.NotLoaded)
            Dispatcher.Dispatch(action: new LoadIgnoredTransactionsAction());

        if (DataLoaded)
        {
            var allTransactions = new List<BankTransaction>();
            allTransactions.AddRange(
                collection: BankTransactionState.Value.Transactions.Where(
                                predicate: tx => tx.TransactionDate > DateTime.Now.AddDays(value: -30)) ??
                            Enumerable.Empty<BankTransaction>());
            allTransactions.AddRange(collection: FutureTransactionState.Value.FutureTransactions ??
                                                 Enumerable.Empty<FutureBankTransaction>());

            var endDateThreshold = DateTime.Today.AddDays(value: 60);
            var potentialTransactions = GenerateTransactionsForNext60Days(
                recurringTransactions: RecurringTransactionsState.Value.RecurringTransactions
                , endDateThreshold: endDateThreshold
                , existingTransactions: allTransactions);
            potentialTransactions = potentialTransactions.Where(predicate: tx => !TransactionIgnored(transaction: tx));
            PossiblyMissingRecurringTransactions = potentialTransactions
               .OrderByDescending(keySelector: tx => tx.TransactionDate)
               .Select(selector: (transaction
                           , index) => new PossiblyMissingTransactionViewModel(transaction: transaction
                                                                               , displayIndex: index))
               .ToList();
        }
    }

    private bool TransactionIgnored(BankTransaction transaction)
    {
        var ignored = IgnoredTransactions.Any(predicate: it =>
                                                  it.TransactionDate.HasValue &&
                                                  it.TransactionDate.Value.Day == transaction.TransactionDate?.Day &&
                                                  it.TransactionDate.Value.Month == transaction.TransactionDate?.Month &&
                                                  it.TransactionDate.Value.Year == transaction.TransactionDate?.Year &&
                                                  it.Description.Equals(value: transaction.Description
                                                                        , comparisonType: StringComparison.OrdinalIgnoreCase) &&
                                                  Math.Abs(value: it.Amount) == Math.Abs(value: transaction.TransactionAmount));
        if (ignored) _logger.LogInformation(message: $" {transaction} was ignored.");

        return ignored;
    }

    private IEnumerable<BankTransaction> GenerateTransactionsForNext60Days(IEnumerable<RecurringTransaction> recurringTransactions
        , DateTime endDateThreshold
        , List<BankTransaction> existingTransactions)
    {
        var date = DateTime.Today;

        //CsvDebugHelper.WriteTransactionsToCsv(existingTransactions, "Data/existingTransactions.csv");
        //CsvDebugHelper.WriteTransactionsToCsv(IgnoredTransactions, "Data/IgnoredTransactions.csv");

        while (date <= endDateThreshold)
        {
            _logger.LogInformation(message: $"---Logging Transactions for {date.ToString(format: "D")}---");
            var recurringTransactionsOnDayOfMonth
                = recurringTransactions.Where(predicate: rt => rt.DayOfMonth == date.Day).ToList();

            if (recurringTransactionsOnDayOfMonth.Any())
                _logger.LogInformation(
                    message: $"\t{recurringTransactionsOnDayOfMonth.Count()} recurring transactions on {date.Day}");

            foreach (var recurring in recurringTransactionsOnDayOfMonth)
            {
                _logger.LogInformation(message: $"\tEvaluating {recurring}");

                if (IsTransactionPresent(date: date
                                         , recurringTransaction: recurring
                                         , existingTransactions: existingTransactions))
                {
                    var matchingTransactions = existingTransactions.Where(predicate: tx =>
                                                                              IsDateWithinTolerance(targetDate: date
                                                                                  , transactionDate: tx.TransactionDate.Value) &&
                                                                              IsAmountWithinTolerance(
                                                                                  actualAmount: Math.Abs(
                                                                                      value: tx.TransactionAmount)
                                                                                  , targetAmount: Math.Abs(
                                                                                      value: recurring.Amount))
                    ).ToList();
                    foreach (var matchingTransaction in matchingTransactions)
                        _logger.LogInformation(message: $"\t\t {matchingTransaction} matched");
                }
                else
                {
                    _logger.LogInformation(message: "\t\t nothing matched");
                    var outGoMultiplier = 1;
                    if (recurring.IsOutgo) outGoMultiplier = -1;
                    yield return new BankTransaction
                    {
                        Id = Guid.NewGuid(),
                        TransactionDate = date
                                     ,
                        TransactionAmount = recurring.Amount * outGoMultiplier,
                        Description = recurring.Description
                                     ,
                        Balance = 0,
                        Taxonomy = recurring.Taxonomy,
                        RecurringTransactionId = recurring.Id
                    };
                }
            }

            date = date.AddDays(value: 1);
        }
    }

    private bool IsTransactionPresent(DateTime date
        , RecurringTransaction recurringTransaction
        , List<BankTransaction> existingTransactions)
    {
        return existingTransactions.Any(predicate: tx =>
                                            IsDateWithinTolerance(targetDate: date
                                                                  , transactionDate: tx.TransactionDate.Value) &&
                                            IsAmountWithinTolerance(actualAmount: Math.Abs(value: tx.TransactionAmount)
                                                                    , targetAmount: Math.Abs(value: recurringTransaction.Amount))
        );
    }

    private bool IsDateWithinTolerance(DateTime targetDate
        , DateTime transactionDate)
    {
        // Determine the range of acceptable dates by subtracting and adding one day to the target date
        var lowerBound = targetDate.AddDays(value: -1);
        var upperBound = targetDate.AddDays(value: 1);

        // Return true if the transaction date is within the inclusive range of lowerBound and upperBound
        return transactionDate >= lowerBound && transactionDate <= upperBound;
    }

    private bool IsAmountWithinTolerance(decimal actualAmount
        , decimal targetAmount)
    {
        var lowerBound = targetAmount - targetAmount * 0.01m; // 1% less
        var upperBound = targetAmount + targetAmount * 0.01m; // 1% more

        return actualAmount >= lowerBound && actualAmount <= upperBound;
    }

    private void SelectAllChanged(bool isChecked)
    {
        foreach (var item in PossiblyMissingRecurringTransactions) item.IsChecked = isChecked;
        IsAllSelected = isChecked;
    }

    private void CheckBoxChanged(bool isChecked
        , PossiblyMissingTransactionViewModel transaction)
    {
        transaction.IsChecked = isChecked;
        IsAllSelected = PossiblyMissingRecurringTransactions.All(predicate: t => t.IsChecked);
    }

    private async Task AddSelectedToFutureTransactions()
    {
        var transactionsToAdd = PossiblyMissingRecurringTransactions
           .Where(predicate: t => t.IsChecked)
           .Select(selector: t => new FutureBankTransaction
           {
               Description = t.Description,
               TransactionAmount = t.Amount
                                      ,
               TransactionDate = t.TransactionDate,
               Taxonomy = t.Taxonomy,
               Enabled = true
           });

        foreach (var futureBankTransaction in transactionsToAdd)
            await FutureTransactionService.AddFutureTransactionAsync(bankTransaction: futureBankTransaction);

        // Reload future transactions to update the UI
        Dispatcher.Dispatch(action: new FutureTransactionActions.LoadFutureTransactionsAction());

        // Clear checkboxes after adding
        foreach (var transaction in PossiblyMissingRecurringTransactions) transaction.IsChecked = false;
        IsAllSelected = false;
        StateHasChanged();
    }

    private void IgnoreTransaction(PossiblyMissingTransactionViewModel transaction)
    {
        Dispatcher.Dispatch(action: new IgnoreTransactionAction(Transaction: transaction));
    }

    public void Dispose()
    {
        RecurringTransactionsState.StateChanged -= HandleStateChange;
        BankTransactionState.StateChanged -= HandleStateChange;
        FutureTransactionState.StateChanged -= HandleStateChange;
        IgnoredTransactionsState.StateChanged -= HandleStateChange;
    }
}