using System.Globalization;
using CSharpFunctionalExtensions;
using CsvHelper;
using CsvHelper.Configuration;
using Fluxor;
using HomeFinances.Models;
using HomeFinances.Store.Account;

namespace HomeFinances.Repositories;

/// <summary>
///     File-based implementation of ICategoryMappingRepository using a CSV file.
/// </summary>
public class FileCategoryMappingRepository : ICategoryMappingRepository, ICategoryRepository, ISubCategoryRepository
{
    // DefaultUser is still used by PrependUserToAccountPath if it needs to add the user segment.
    private const string DefaultUser = "gordon";

    private static readonly string[] KnownAccountNames = { "BeeHive", "CitiBank", "MountainAmerica" };
    private static readonly string BaseDataPath = "Data";
    private readonly CsvConfiguration _csvConfig;
    private readonly string _filePath;
    private readonly List<CategoryMapping> _mappings;

    public FileCategoryMappingRepository(IState<AccountState> accountStateAccessor)
    {
        var currentAccount
            = accountStateAccessor.Value.AvailableAccounts.FirstOrDefault(
                predicate: a => a.Id == accountStateAccessor.Value.CurrentAccountId);
        var currentAccountName = currentAccount?.Name ?? "BeeHive";

        var accountSpecificPath = Path.Combine(path1: BaseDataPath
                                               , path2: currentAccountName
                                               , path3: "Mappings.csv");
        _filePath = PrependUserToAccountPath(originalFilePath: accountSpecificPath);

        _csvConfig = new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
                     {
                         HasHeaderRecord = true
                     };
        EnsureFileExists();
        _mappings = LoadMappingsFromFile();
    }

    public Task<Result<List<CategoryMapping>>> GetAllMappingsAsync()
    {
        var copy = _mappings.Select(selector: m => new CategoryMapping
                                                   {
                                                       Description = m.Description, Category = m.Category
                                                       , SubCategory = m.SubCategory
                                                   }).ToList();
        return Task.FromResult(result: Result.Success(value: copy));
    }

    public Task<Result<CategoryMapping?>> GetMappingByDescriptionAsync(string description)
    {
        var mapping = _mappings.FirstOrDefault(predicate: m => string.Equals(a: m.Description
                                                                             , b: description
                                                                             , comparisonType: StringComparison
                                                                                .OrdinalIgnoreCase));
        if (mapping == null) return Task.FromResult(result: Result.Success<CategoryMapping?>(value: null));
        var result = new CategoryMapping
                     {
                         Description = mapping.Description, Category = mapping.Category, SubCategory = mapping.SubCategory
                     };
        return Task.FromResult(result: Result.Success<CategoryMapping?>(value: result));
    }

    public Task<Result> AddOrUpdateMappingAsync(CategoryMapping mapping)
    {
        if (mapping == null)
            return Task.FromResult(result: Result.Failure(error: "Mapping cannot be null."));

        var existing = _mappings.FirstOrDefault(predicate: m =>
                                                    string.Equals(a: m.Description
                                                                  , b: mapping.Description
                                                                  , comparisonType: StringComparison.OrdinalIgnoreCase));

        if (existing != null)
        {
            existing.Category = mapping.Category;
            existing.SubCategory = mapping.SubCategory;
        }
        else
        {
            _mappings.Add(item: new CategoryMapping
                                {
                                    Description = mapping.Description, Category = mapping.Category
                                    , SubCategory = mapping.SubCategory
                                });
        }

        WriteAllMappingsToFile();
        return Task.FromResult(result: Result.Success());
    }

    public Task<Result<bool>> RemoveMappingAsync(string description)
    {
        var mapping = _mappings.FirstOrDefault(predicate: m => string.Equals(a: m.Description
                                                                             , b: description
                                                                             , comparisonType: StringComparison
                                                                                .OrdinalIgnoreCase));
        if (mapping != null)
        {
            _mappings.Remove(item: mapping);
            WriteAllMappingsToFile();
            return Task.FromResult(result: Result.Success(value: true));
        }

        return Task.FromResult(result: Result.Success(value: false));
    }

    public Task<Result<List<string>>> GetAllCategoriesAsync()
    {
        var categories = _mappings.Select(selector: m => m.Category).Distinct().ToList();
        return Task.FromResult(result: Result.Success(value: categories));
    }

    public Task<Result> AddCategoryAsync(string category)
    {
        if (string.IsNullOrWhiteSpace(value: category))
            return Task.FromResult(result: Result.Failure(error: "Category cannot be empty."));

        if (_mappings.Any(predicate: m => m.Category.Equals(value: category
                                                            , comparisonType: StringComparison.OrdinalIgnoreCase)))
            return Task.FromResult(result: Result.Failure(error: "Category already exists."));

        _mappings.Add(item: new CategoryMapping { Category = category });
        WriteAllMappingsToFile();
        return Task.FromResult(result: Result.Success());
    }

    public Task<Result<List<string>>> GetSubCategoriesAsync(string category)
    {
        if (string.IsNullOrWhiteSpace(value: category))
            return Task.FromResult(result: Result.Success(value: new List<string>()));

        var subCategories = _mappings
           .Where(predicate: m => string.Equals(a: m.Category
                                                , b: category
                                                , comparisonType: StringComparison.OrdinalIgnoreCase) &&
                                  !string.IsNullOrWhiteSpace(value: m.SubCategory))
           .Select(selector: m => m.SubCategory)
           .Distinct()
           .ToList();

        return Task.FromResult(result: Result.Success(value: subCategories));
    }

    public Task<Result> AddSubCategoryAsync(string category
        , string subCategory)
    {
        if (string.IsNullOrWhiteSpace(value: category) || string.IsNullOrWhiteSpace(value: subCategory))
            return Task.FromResult(result: Result.Failure(error: "Category and subcategory cannot be empty."));

        // Check if mapping already exists
        var exists = _mappings.Any(predicate: m =>
                                       string.Equals(a: m.Category
                                                     , b: category
                                                     , comparisonType: StringComparison.OrdinalIgnoreCase) &&
                                       string.Equals(a: m.SubCategory
                                                     , b: subCategory
                                                     , comparisonType: StringComparison.OrdinalIgnoreCase));

        if (exists)
            return Task.FromResult(result: Result.Failure(error: "Subcategory already exists for this category."));

        _mappings.Add(item: new CategoryMapping
                            {
                                Category = category, SubCategory = subCategory
                            });

        WriteAllMappingsToFile();
        return Task.FromResult(result: Result.Success());
    }

    public Task<Result<bool>> RemoveSubCategoryAsync(string category
        , string subCategory)
    {
        var mapping = _mappings.FirstOrDefault(predicate: m => m.Category.Equals(value: category
                                                                                 , comparisonType: StringComparison
                                                                                    .OrdinalIgnoreCase));
        if (mapping != null)
        {
            var subCat = mapping.SubCategory;
            if (subCat.Equals(value: subCategory
                              , comparisonType: StringComparison.OrdinalIgnoreCase))
            {
                mapping.SubCategory = null; // Assuming subcategory removal means setting it to null
                WriteAllMappingsToFile();
                return Task.FromResult(result: Result.Success(value: true));
            }
        }

        return Task.FromResult(result: Result.Success(value: false));
    }

    private string PrependUserToAccountPath(string originalFilePath)
    {
        var pathForProcessing = originalFilePath.Replace(oldChar: Path.AltDirectorySeparatorChar
                                                         , newChar: Path.DirectorySeparatorChar)
           .Replace(oldValue: Path.DirectorySeparatorChar.ToString() + Path.DirectorySeparatorChar
                    , newValue: Path.DirectorySeparatorChar.ToString());
        var segments = new List<string>(collection: pathForProcessing.Split(separator: Path.DirectorySeparatorChar));

        var accountSegmentIndex = -1;

        for (var i = 0; i < segments.Count; i++)
        {
            var currentSegment = segments[index: i];
            foreach (var knownAccount in KnownAccountNames)
                if (currentSegment.Equals(value: knownAccount
                                          , comparisonType: StringComparison.OrdinalIgnoreCase))
                {
                    // Check if this account segment is already prefixed by DefaultUser
                    if (i > 0 && segments[index: i - 1].Equals(value: DefaultUser
                                                               , comparisonType: StringComparison.OrdinalIgnoreCase))
                        return originalFilePath; // Path is already in user/account format
                    accountSegmentIndex = i;
                    goto found_account_segment; // Exit both loops
                }
        }

        found_account_segment:
        if (accountSegmentIndex != -1)
        {
            segments.Insert(index: accountSegmentIndex
                            , item: DefaultUser);
            // Reconstruct the path using the system's primary directory separator.
            return string.Join(separator: Path.DirectorySeparatorChar.ToString()
                               , values: segments);
        }

        return originalFilePath; // No known account found, or structure doesn't match expectations
    }

    private void EnsureFileExists()
    {
        var directory = Path.GetDirectoryName(path: _filePath);
        if (!string.IsNullOrEmpty(value: directory) && !Directory.Exists(path: directory))
            Directory.CreateDirectory(path: directory);
        if (!File.Exists(path: _filePath))
        {
            using var writer = new StreamWriter(path: _filePath
                                                , append: false);
            using var csv = new CsvWriter(writer: writer
                                          , configuration: _csvConfig);
            csv.WriteHeader<CategoryMapping>();
            csv.NextRecord();
        }
    }

    private List<CategoryMapping> LoadMappingsFromFile()
    {
        using var reader = new StreamReader(path: _filePath);
        using var csv = new CsvReader(reader: reader
                                      , configuration: _csvConfig);
        var records = csv.GetRecords<CategoryMapping>().ToList();
        return records;
    }

    private void WriteAllMappingsToFile()
    {
        using var writer = new StreamWriter(path: _filePath
                                            , append: false);
        using var csv = new CsvWriter(writer: writer
                                      , configuration: _csvConfig);
        csv.WriteHeader<CategoryMapping>();
        csv.NextRecord();
        foreach (var m in _mappings)
        {
            csv.WriteRecord(record: m);
            csv.NextRecord();
        }
    }
}