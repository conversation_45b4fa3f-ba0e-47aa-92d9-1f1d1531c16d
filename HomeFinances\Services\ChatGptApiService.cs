﻿using System.Text;
using System.Text.Json;
using HomeFinances.Models;
using HomeFinances.Models.ChatGPT;

namespace HomeFinances.Services;

public class ChatGptApiService
{
    private readonly List<string> _categories;
    private readonly CategoryService _categoryService;
    private readonly IConfiguration _configuration;
    private readonly HttpClient _httpClient;
    private readonly ImageStorageService _imageStorageService;
    private readonly ILogger<ChatGptApiService> _logger;
    private readonly string _requestData;
    private ModelRequestData _requestDataModel;
    private bool _sessionInitialized;

    public ChatGptApiService(HttpClient httpClient
        , IConfiguration configuration
        , CategoryService categoryService
        , ImageStorageService imageStorageService
        , ILogger<ChatGptApiService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _requestData = File.ReadAllText(path: "Data/Categories.csv");
        _categoryService = categoryService;
        _categories = categoryService.GetAllCategoriesAndSubcategories();
        _imageStorageService = imageStorageService;
        _logger = logger;
    }

    public async Task InitializeSession()
    {
        var categoriesString = string.Join(separator: "; "
                                           , values: _categories);

        var initialPrompt = $@"Initialize session with existing categories and subcategories: [{categoriesString}].
Based on the transaction description, please return a structured JSON response with the most likely categories and subcategories.
If a suitable subcategory does not exist, feel free to suggest a new one, but ensure that the category is appropriate.
Additionally, include a list of tags based on the transaction details. The tags should be selected from the following options: 'Amazon', 'Walmart', 'Debt', 'Luxury', 'Nice to Have', 'Wants', 'Needs', 'Emergency', 'Medical'.
The response should be formatted as follows:

{{
  ""Category"": ""ExampleCategory"",
  ""SubCategory"": ""ExampleSubCategory"",
  ""Notes"": ""This is an example note explaining why the transaction was categorized this way."",
  ""Tags"": [""ExampleTag1"", ""ExampleTag2""]
}}";

        _requestDataModel = new ModelRequestData
        {
            Model = "gpt-4o-mini",
            Messages = new List<Message>
                                                                  {
                                                                      new() { Role = "system", Content = initialPrompt }
                                                                  }
                                ,
            Temperature = 0.7,
            MaxTokens = 1000
        };

        // Serialize and send the request as before, but only once to establish session context
        // No need to repeat this for every transaction, hence the _sessionInitialized flag
        if (!_sessionInitialized) _sessionInitialized = true; // Mark session as initialized
    }

    private async Task<ChatCompletionResponse> GetChatCompletion()
    {
        var json = JsonSerializer.Serialize(value: _requestDataModel);
        using var content = new StringContent(content: json
                                              , encoding: Encoding.UTF8
                                              , mediaType: "application/json");

        try
        {
            var response = await _httpClient.PostAsync(requestUri: "https://api.openai.com/v1/chat/completions"
                                                       , content: content);
            response.EnsureSuccessStatusCode();
            var responseBody = await response.Content.ReadAsStringAsync();

            var chatCompletionResponse = JsonSerializer.Deserialize<ChatCompletionResponse>(json: responseBody);
            return chatCompletionResponse;
        }
        catch (HttpRequestException e)
        {
            _logger.LogError(exception: e
                             , message: "HTTP request failed");
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "An error occurred");
        }

        return null; // Return null in case of failure
    }

    public async Task<Taxonomy> ProcessReceipt(string imagePath)
    {
        _logger.LogInformation(message: "Starting receipt processing for image: {ImagePath}"
                               , imagePath);

        // First, upload the image to get a URL
        var imageUrl = await _imageStorageService.UploadImageAsync(imagePath: imagePath);
        if (string.IsNullOrEmpty(value: imageUrl))
        {
            _logger.LogError(message: "Failed to upload image to storage");
            throw new InvalidOperationException(message: "Failed to upload image");
        }

        _logger.LogInformation(message: "Image uploaded successfully. URL: {ImageUrl}"
                               , imageUrl);

        var ocrText = await ExtractTextFromReceiptImage(imagePath: imagePath);
        _logger.LogInformation(message: "OCR Text extracted: {OcrText}"
                               , ocrText);

        var transaction = new BeeHiveTransaction { Description = ocrText };
        var categorization = await GetCategoryGuess(bankTransaction: transaction);
        _logger.LogInformation(message: "Transaction categorized: Category={Category}, SubCategory={SubCategory}"
                               ,
                               categorization.Category
                               , categorization.SubCategory);

        var amount = await ExtractAmountFromTextAsync(receiptText: ocrText);
        _logger.LogInformation(message: "Amount extracted: {Amount}"
                               , amount);

        var taxonomy = new Taxonomy
        {
            Category = categorization.Category,
            SubCategory = categorization.SubCategory,
            Amount = amount
                           ,
            Details = ocrText,
            Confidence = 100, // Since we're using GPT-4, we can set a high confidence
            ImageUrl = imageUrl
        };

        _logger.LogInformation(message: "Final taxonomy created: {@Taxonomy}"
                               , taxonomy);
        return taxonomy;
    }

    public async Task<Taxonomy> GetCategoryGuess(BankTransaction bankTransaction)
    {
        if (_requestDataModel == null)
            await InitializeSession();

        var categoryDescriptions = _categories
           .Select(selector: category => $"{category}: {string.Join(separator: ", ", values: category)}")
           .ToList();
        var categoriesString = string.Join(separator: "; "
                                           , values: categoryDescriptions);

        // Updated prompt including instructions to return a confidence value.
        var prompt = $@"You are a personal accountant. Using the existing categories and subcategories: [{categoriesString}].
Based on the transaction description, please return a JSON response with the following keys:
  - Category (string)
  - SubCategory (string)
  - Confidence (number from 1 to 100, where 100 means highest confidence)
  - Tags (list of strings selected from: 'Amazon', 'Walmart', 'Debt', 'Luxury', 'Nice to Have', 'Wants', 'Needs', 'Emergency', 'Medical')

The response should be in plain JSON format with no markdown or extra formatting.

For example:
{{
  ""Category"": ""ExampleCategory"",
  ""SubCategory"": ""ExampleSubCategory"",
  ""Confidence"": 95,
  ""Tags"": [""ExampleTag1"", ""ExampleTag2""]
}}

Given the financial transaction with the description '{bankTransaction.Description}', please generate the response as described.";

        _requestDataModel.Messages = new List<Message>
                                     {
                                         new() { Role = "user", Content = prompt }
                                     };

        var chatCompletionResponse = await GetChatCompletion();
        if (chatCompletionResponse != null && chatCompletionResponse.Choices.Any())
        {
            var choice = chatCompletionResponse.Choices.FirstOrDefault();
            // Assuming CategoryGuess now has Confidence and Tags properties
            var categoryGuess = JsonSerializer.Deserialize<CategoryGuess>(json: choice.Message.Content.ToString());
            if (categoryGuess != null)
                return new Taxonomy
                {
                    Category = categoryGuess.Category,
                    SubCategory = categoryGuess.SubCategory
                           ,
                    Confidence = categoryGuess.Confidence,
                    Tags = categoryGuess.Tags ?? new List<string>()
                };
        }

        // Fallback Taxonomy object
        return new Taxonomy
        {
            Category = "Unknown",
            SubCategory = "Unknown",
            Confidence = 0,
            Tags = new List<string>()
        };
    }

    public async Task<Taxonomy> CategorizeTransactionFromReceiptImage(string imageUrl)
    {
        try
        {
            // Step 1: Extract OCR text from the receipt image.
            var ocrText = await ExtractTextFromReceiptImage(imagePath: imageUrl);
            _logger.LogInformation(message: "OCR Text: {OcrText}"
                                   , ocrText);

            // Step 2: Get categorization based on the OCR text.
            var categorization = await GetCategoryGuess(bankTransaction: new BankTransaction { Description = ocrText });
            _logger.LogInformation(message: "Categorization: Category = {Category}, SubCategory = {SubCategory}"
                                   ,
                                   categorization.Category
                                   , categorization.SubCategory);

            // Step 3: Extract the amount from the OCR text via another ChatGPT API call.
            var amount = await ExtractAmountFromTextAsync(receiptText: ocrText);
            _logger.LogInformation(message: "Extracted Amount: {Amount}"
                                   , amount);

            // Update the categorization with the extracted amount.
            categorization.Amount = amount;

            return categorization;
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error categorizing transaction from receipt image");
            // Return a default Taxonomy object if an error occurs.
            return new Taxonomy
            {
                Category = "Unknown",
                SubCategory = "Unknown",
                Tags = new List<string>(),
                Amount = 0m
            };
        }
    }

    public async Task<decimal> ExtractAmountFromTextAsync(string receiptText)
    {
        _logger.LogInformation(message: "Starting amount extraction from receipt text: {ReceiptText}"
                               , receiptText);

        var prompt = "Extract the amount from the following receipt text. Return only the number, nothing else:\n\n" +
                     receiptText;

        _logger.LogInformation(message: "Sending prompt to ChatGPT: {Prompt}"
                               , prompt);

        var requestData = new ModelRequestData
        {
            Model = "gpt-4",
            Messages = new List<Message> { new() { Role = "user", Content = prompt } }
                              ,
            MaxTokens = 100,
            Temperature = 0
        };

        var json = JsonSerializer.Serialize(value: requestData);
        using var content = new StringContent(content: json
                                              , encoding: Encoding.UTF8
                                              , mediaType: "application/json");

        try
        {
            var response = await _httpClient.PostAsync(requestUri: "https://api.openai.com/v1/chat/completions"
                                                       , content: content);
            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogInformation(message: "Received response from ChatGPT: {Response}"
                                   , responseContent);

            if (response.IsSuccessStatusCode)
            {
                var chatCompletionResponse = JsonSerializer.Deserialize<ChatCompletionResponse>(json: responseContent);
                if (chatCompletionResponse?.Choices?.Any() == true)
                {
                    var amountText = chatCompletionResponse.Choices[index: 0].Message.Content.ToString().Trim();
                    _logger.LogInformation(message: "Extracted amount text: {AmountText}"
                                           , amountText);

                    if (decimal.TryParse(s: amountText
                                         , result: out var amount))
                    {
                        _logger.LogInformation(message: "Successfully parsed amount: {Amount}"
                                               , amount);
                        return amount;
                    }

                    _logger.LogWarning(message: "Failed to parse amount from text: {AmountText}"
                                       , amountText);
                }
            }
            else
            {
                _logger.LogError(message: "ChatGPT API error: {StatusCode} {Content}"
                                 , response.StatusCode
                                 , responseContent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "Error extracting amount from receipt text");
        }

        _logger.LogWarning(message: "Failed to extract amount from receipt text");
        return 0m;
    }

    public async Task<string> ExtractTextFromReceiptImage(string imagePath)
    {
        try
        {
            var textContent = new Content
            {
                Type = "text",
                Text = "Please analyze this receipt and extract the text content."
            };

            var imageContent = new Content
            {
                Type = "image_url",
                ImageUrl = new ImageUrl
                {
                    Url
                                                                          = $"data:image/png;base64,{await EncodeImageToBase64(imagePath: imagePath)}"
                }
            };

            var request = new ModelRequestData
            {
                Model = "gpt-4o",
                Messages = new List<Message>
                                                           {
                                                               new()
                                                               {
                                                                   Role = "user", Content = new[] { textContent, imageContent }
                                                               }
                                                           }
                              ,
                MaxTokens = 4096
            };

            File.WriteAllText(
                path: @"C:\Users\<USER>\source\repos\HomeFinances\HomeFinances.Test\bin\Debug\net8.0\logs\requestPayload" +
                      $"{Guid.NewGuid()}.json",
                contents: JsonSerializer.Serialize(value: request
                                                   , options: new JsonSerializerOptions { WriteIndented = true }));

            _logger.LogInformation(message: "Sending receipt to ChatGPT for analysis: {Path}"
                                   , imagePath);

            using (var response = await _httpClient.PostAsJsonAsync(requestUri: "https://api.openai.com/v1/chat/completions"
                                                                    , value: request))
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogInformation(message: "ChatGPT Response: {Response}"
                                       , responseContent);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError(message: "Failed to get response from ChatGPT: {StatusCode} {Response}"
                                     ,
                                     response.StatusCode
                                     , responseContent);
                    return string.Empty;
                }

                var result = JsonSerializer.Deserialize<ChatCompletionResponse>(json: responseContent);
                var content = result?.Choices?.FirstOrDefault()?.Message?.Content;

                if (content is string responseText)
                    return responseText;

                return content?.ToString() ?? string.Empty;
            }
        }
        catch (Exception ex) when (ex is not FileNotFoundException)
        {
            _logger.LogError(exception: ex
                             , message: "Error extracting text from receipt image");
            return string.Empty;
        }
    }

    private async Task<string> EncodeImageToBase64(string imagePath)
    {
        var imageBytes = await File.ReadAllBytesAsync(path: imagePath);
        var base64Image = Convert.ToBase64String(inArray: imageBytes);
        return base64Image;
    }

    public async Task<string> GenerateImageFromDescription(string description)
    {
        var imageModel = "dall-e-3";
        var requestData = new
        {
            model = imageModel,
            prompt = description,
            n = 1,
            size = "1024x1024"
        };

        try
        {
            var response = await PostImageGenerationRequestAsync(url: "https://api.openai.com/v1/images/generations"
                                                                 , requestData: requestData);
            var responseBody = await response.Content.ReadAsStringAsync();

            var imageResponse = JsonSerializer.Deserialize<DalleImageResponse>(json: responseBody);
            if (imageResponse != null && imageResponse.Urls.Length > 0)
            {
                var imageUrl = imageResponse.Urls.First().Url;
                if (!string.IsNullOrEmpty(value: imageUrl))
                {
                    var permanentImageUrl = await _imageStorageService.StoreImageAsync(imageUrl: imageUrl
                        , prompt: "Receipt analysis");
                    return permanentImageUrl;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "An error occurred while generating the image.");
        }

        return null;
    }

    private async Task<HttpResponseMessage> PostImageGenerationRequestAsync(string url
        , object requestData)
    {
        var json = JsonSerializer.Serialize(value: requestData);
        using var content = new StringContent(content: json
                                              , encoding: Encoding.UTF8
                                              , mediaType: "application/json");

        try
        {
            var response = await _httpClient.PostAsync(requestUri: url
                                                       , content: content);
            response.EnsureSuccessStatusCode();
            return response;
        }
        catch (HttpRequestException e)
        {
            _logger.LogError(exception: e
                             , message: "HTTP request failed");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex
                             , message: "An error occurred");
            throw;
        }
    }
}