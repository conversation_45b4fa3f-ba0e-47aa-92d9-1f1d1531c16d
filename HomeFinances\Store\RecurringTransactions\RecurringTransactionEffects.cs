﻿using Fluxor;
using HomeFinances.Models;
using HomeFinances.Services;
using System.Net.Http;
using static HomeFinances.Store.RecurringTransactions.RecurringTransactionActions;

namespace HomeFinances.Store.RecurringTransactions
{
    public class RecurringTransactionEffects
    {
        private readonly RecurringTransactionService _transactionFileService;

        public RecurringTransactionEffects(RecurringTransactionService transactionFileService)
        {
            _transactionFileService = transactionFileService;
        }
        [EffectMethod]
        public async Task HandleLoadTransactions(RecurringTransactionActions.LoadRecurringTransactionsAction action, IDispatcher dispatcher)
        {
            try
            {
                var transactions = await _transactionFileService.LoadTransactionsAsync();
                dispatcher.Dispatch(new RecurringTransactionActions.LoadRecurringTransactionsSuccessAction(transactions));
            }
            catch (Exception ex)
            {
                dispatcher.Dispatch(new RecurringTransactionActions.LoadRecurringTransactionsFailedAction(ex.Message));
            }
        }

        [EffectMethod]
        public async Task HandleAddRecurringTransactionAction(AddOrUpdateRecurringTransactionAction action, IDispatcher dispatcher)
        {
            try
            {
                await _transactionFileService.AddOrUpdateTransaction(action.Transaction);
                dispatcher.Dispatch(new AddOrUpdateRecurringTransactionSuccessAction(action.Transaction));
            }
            catch (Exception ex)
            {
                dispatcher.Dispatch(new AddOrUpdateRecurringTransactionFailedAction(ex.Message));
            }
        }

        [EffectMethod]
        public async Task HandleDeleteRecurringTransactionAction(DeleteRecurringTransactionAction action, IDispatcher dispatcher)
        {
            try
            {
                await _transactionFileService.DeleteTransaction(action.TransactionId);
                dispatcher.Dispatch(new DeleteRecurringTransactionSuccessAction(action.TransactionId));
            }
            catch (Exception ex)
            {
                dispatcher.Dispatch(new DeleteRecurringTransactionFailedAction(ex.Message));
            }
        }
    }
}