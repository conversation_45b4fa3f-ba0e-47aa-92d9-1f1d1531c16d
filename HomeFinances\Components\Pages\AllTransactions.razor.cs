﻿using Fluxor;
using HomeFinances.Models;
using HomeFinances.Models.Mapping;
using HomeFinances.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using static HomeFinances.Store.BankTransactions.BankTransactionActions;

namespace HomeFinances.Components.Pages
{
    public partial class AllTransactions : ComponentBase
    {
        [Inject] private FutureTransactionService futureTransactionService { get; set; }
        [Inject] private IJSRuntime JsRuntime { get; set; }

        private List<BankTransaction> SortedTransactions = new List<BankTransaction>();
        private double windowWidth;
        protected override void OnInitialized()
        {
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender) //TODO this is repeated logic it seems like globablizing this and maintaining it as part of the app state would make more sense.
            {
                await JsRuntime.InvokeVoidAsync("registerResizeListener", DotNetObjectReference.Create(this));
                await GetWindowWidth();
            }
        }
        private async Task GetWindowWidth()
        {
            windowWidth = await JsRuntime.InvokeAsync<double>("blazorResize.getInnerWidth");
            StateHasChanged();
        }
        [JSInvokable]
        public async Task OnBrowserResize()
        {
            await GetWindowWidth();
        }
    }
}