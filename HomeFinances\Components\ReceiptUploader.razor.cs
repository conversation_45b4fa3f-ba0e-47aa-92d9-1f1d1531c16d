using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using HomeFinances.Models;
using HomeFinances.Services;
using HomeFinances.Components.Dialogs;
using MudBlazor;
using Microsoft.Extensions.Logging;
using Fluxor;
using static HomeFinances.Store.FutureTransactions.FutureTransactionActions;

namespace HomeFinances.Components;

public partial class ReceiptUploader : ComponentBase
{
    [Inject] private IDialogService DialogService { get; set; } = default!;
    [Inject] private FileService FileService { get; set; } = default!;
    [Inject] private ISnackbar Snackbar { get; set; } = default!;
    [Inject] private ILogger<ReceiptUploader> Logger { get; set; } = default!;
    [Inject] private IDispatcher Dispatcher { get; set; } = default!;

    private string uploadMessage = string.Empty;
    private bool isSuccess;
    private bool isProcessing;

    private async Task UploadFile(IBrowserFile file)
    {
        try
        {
            isProcessing = true;
            StateHasChanged();

            Logger.LogInformation("Processing receipt upload: {FileName}", file.Name);
            var (transaction, receiptPath) = await FileService.ProcessReceiptUpload(file);
            
            Logger.LogInformation("Showing receipt processing dialog for {FileName}", file.Name);
            var parameters = new DialogParameters
            {
                { nameof(ReceiptProcessingDialog.Transaction), transaction },
                { nameof(ReceiptProcessingDialog.ReceiptPath), receiptPath }
            };

            var options = new DialogOptions
            {
                MaxWidth = MaxWidth.Large,
                FullWidth = true,
                CloseButton = true,
                DisableBackdropClick = true
            };

            var dialog = await DialogService.ShowAsync<ReceiptProcessingDialog>("Process Receipt", parameters, options);
            var result = await dialog.Result;

            if (!result.Canceled)
            {
                Logger.LogInformation("Saving processed receipt transaction");
                var processedTransaction = (FutureBankTransaction)result.Data;
                await FileService.SaveFutureTransaction(processedTransaction);
                uploadMessage = "Receipt processed and saved successfully.";
                isSuccess = true;
                Snackbar.Add("Receipt processed and saved successfully.", Severity.Success);
                
                // Reload future transactions to update the UI
                Dispatcher.Dispatch(new LoadFutureTransactionsAction());
            }
            else
            {
                uploadMessage = "Receipt processing cancelled.";
                isSuccess = false;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing receipt");
            uploadMessage = $"Error processing receipt: {ex.Message}";
            isSuccess = false;
            Snackbar.Add($"Error processing receipt: {ex.Message}", Severity.Error);
        }
        finally
        {
            isProcessing = false;
            StateHasChanged();
        }
    }
} 