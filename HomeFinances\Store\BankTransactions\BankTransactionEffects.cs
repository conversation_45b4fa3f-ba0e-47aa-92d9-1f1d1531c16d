﻿using System.Diagnostics;
using Fluxor;
using HomeFinances.Services;
using static HomeFinances.Store.BankTransactions.BankTransactionActions;

namespace HomeFinances.Store.BankTransactions
{
    public class BankTransactionEffects
    {
        private readonly TransactionReadWriteService _transactionReadWriteService;
        private readonly CategoryService _categoryService;
        private readonly FutureTransactionService _futureTransactionService;

        public BankTransactionEffects(TransactionReadWriteService transactionReadWriteService, CategoryService categoryService, FutureTransactionService futureTransactionService)
        {
            _transactionReadWriteService = transactionReadWriteService;
            _categoryService = categoryService;
            _futureTransactionService = futureTransactionService;
        }

        [EffectMethod]
        public async Task HandleLoadTransactionsAction(LoadTransactionsAction action, IDispatcher dispatcher)
        {
            try
            {
                var transactions = await _transactionReadWriteService.ReadBankTransactionsAsync("BankTransactions.csv");

                dispatcher.Dispatch(new LoadTransactionsSuccessAction(transactions));
            }
            catch (Exception ex)
            {
                dispatcher.Dispatch(new LoadTransactionsFailedAction(ex.Message));
            }
        }

        [EffectMethod]
        public async Task HandleMergeBeehiveTransactionsAction(MergeBeehiveTransactionsAction action, IDispatcher dispatcher)
        {
            try
            {
                var beehiveTransactions = action.BeeHiveTransactions;
                // Merge and save transactions using the repository
                var mergedTransactions = await _transactionReadWriteService.MergeAndSaveBeehiveTransactions(beehiveTransactions, "BankTransactions.csv");
                dispatcher.Dispatch(new MergeBeehiveTransactionsSuccessAction(mergedTransactions));
            }
            catch (Exception ex)
            {
                dispatcher.Dispatch(new MergeBeehiveTransactionsFailedAction(ex.Message));
            }
        }

    }
}