﻿@using HomeFinances.Models
@using HomeFinances.Services
@inject FileService FileService
@inject FutureTransactionService FutureTransactionService
@inject ChatGptApiService ChatGpt
@inject ISnackbar Snackbar

<MudContainer>
    <MudText Typo="Typo.h4">Receipt Processing</MudText>
    
    <label for="file-upload">
        <MudButton HtmlTag="label"
                   Variant="Variant.Filled"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.CloudUpload"
                   for="file-upload">
            Upload Receipt
        </MudButton>
    </label>

    <MudFileUpload T="IBrowserFile" 
                   Id="file-upload"
                   Style="display: none;"
                   Accept="image/*" 
                   FilesChanged="UploadFile" 
                   MaximumFileCount="1">
    </MudFileUpload>

    @if (filePaths.Any())
    {
        <MudText Typo="Typo.h6" Class="mt-4">Recent Receipts</MudText>
        <MudList>
            @foreach (var filePath in filePaths)
            {
                <MudListItem>
                    <MudButton OnClick="@(() => ProcessImageFile(filePath))"
                              StartIcon="@Icons.Material.Filled.Receipt">
                        @Path.GetFileName(filePath)
                    </MudButton>
                </MudListItem>
            }
        </MudList>
    }
</MudContainer>

@code {
    private List<string> filePaths = new List<string>();

    protected override void OnInitialized()
    {
        filePaths = FileService.GetMostRecentFiles();
    }

    private async Task UploadFile(IBrowserFile file)
    {
        if (file != null)
        {
            try
            {
                var maxAllowedSize = 1024 * 1024 * 10; // 10 MB limit
                if (file.Size > maxAllowedSize)
                {
                    Snackbar.Add("File size exceeds the maximum allowed limit of 10 MB.", Severity.Error);
                    return;
                }

                var buffer = new MemoryStream();
                await file.OpenReadStream(maxAllowedSize).CopyToAsync(buffer);
                buffer.Seek(0, SeekOrigin.Begin);

                var uploadsDir = Path.Combine("Data", "Uploads", "Receipts");
                Directory.CreateDirectory(uploadsDir);
                var savePath = Path.Combine(uploadsDir, $"{DateTime.Now:yyyy-MM-dd-HH-mm-ss}_{file.Name}");

                using (var fileStream = File.Create(savePath))
                {
                    buffer.Seek(0, SeekOrigin.Begin);
                    await buffer.CopyToAsync(fileStream);
                }

                filePaths.Insert(0, savePath);
                Snackbar.Add("Receipt uploaded successfully.", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error uploading file: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ProcessImageFile(string filePath)
    {
        try
        {
            var description = await ChatGpt.ExtractTextFromReceiptImage(filePath);
            
            var futureTransaction = new FutureBankTransaction
            {
                Description = description,
                TransactionDate = DateTime.Now,
                // Set other necessary properties
            };

            await FutureTransactionService.AddFutureTransactionAsync(futureTransaction);
            Snackbar.Add("Receipt processed successfully.", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error processing receipt: {ex.Message}", Severity.Error);
        }
    }
}