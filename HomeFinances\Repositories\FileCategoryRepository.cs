using System.Globalization;
using CSharpFunctionalExtensions;
using CsvHelper;
using CsvHelper.Configuration;
using Fluxor;
using HomeFinances.Store.Account;

namespace HomeFinances.Repositories;

/// <summary>
///     File-based implementation of ICategoryRepository using a CSV file.
/// </summary>
public class FileCategoryRepository : ICategoryRepository, ISubCategoryRepository
{
    private const string DefaultUser = "gordon";
    private readonly string _baseDataPath;
    private readonly Dictionary<string, List<string>> _categoryMappings;
    private readonly CsvConfiguration _csvConfig;
    private readonly string _filePath;
    private readonly string[] _knownAccountNames;

    public FileCategoryRepository(IState<AccountState> accountStateAccessor)
    {
        _knownAccountNames = new[] { "BeeHive", "CitiBank", "MountainAmerica" };
        _baseDataPath = "Data";

        var currentAccount
            = accountStateAccessor.Value.AvailableAccounts.FirstOrDefault(
                predicate: a => a.Id == accountStateAccessor.Value.CurrentAccountId);
        var currentAccountName = currentAccount?.Name ?? "BeeHive";

        var accountSpecificPath = Path.Combine(path1: _baseDataPath
                                               , path2: currentAccountName
                                               , path3: "Categories.csv");
        _filePath = PathHelper.PrependUserToAccountPath(originalFilePath: accountSpecificPath
                                                        , knownAccountNames: _knownAccountNames);

        _csvConfig = CreateCsvConfiguration();
        FileHelper.EnsureFileExists(filePath: _filePath
                                    , csvConfig: _csvConfig);
        _categoryMappings = FileHelper.LoadMappingsFromFile(filePath: _filePath
                                                            , csvConfig: _csvConfig);
    }

    public Task<Result<List<string>>> GetAllCategoriesAsync()
    {
        return Task.Run(function: () => ExecuteSafely(function: () =>
        {
            var categoryMappingsCopy = CopyCategoryMappings();
            return Result.Success(value: categoryMappingsCopy.Keys.ToList());
        }));
    }

    public Task<Result> AddCategoryAsync(string category)
    {
        ExecuteSafely(function: () =>
        {
            if (!string.IsNullOrWhiteSpace(value: category))
                if (!_categoryMappings.ContainsKey(key: category))
                {
                    _categoryMappings[key: category] = new List<string>();
                    FileHelper.AppendToFile(filePath: _filePath
                                            , category: category
                                            , subCategory: null);
                }
        });

        return Task.FromResult(result: Result.Success());
    }

    public Task<Result<List<string>>> GetSubCategoriesAsync(string category)
    {
        return Task.Run(function: () => ExecuteSafely(function: () =>
        {
            return Result.Success(value: GetSubCategoryList(category: category));
        }));
    }

    public Task<Result> AddSubCategoryAsync(string category
        , string subCategory)
    {
        ExecuteSafely(function: () =>
        {
            AddOrUpdateSubCategory(category: category
                                   , subCategory: subCategory);
        });

        return Task.FromResult(result: Result.Success());
    }

    public Task<Result<bool>> RemoveSubCategoryAsync(string category
        , string subCategory)
    {
        return Task.Run(function: () => ExecuteSafely(function: () =>
        {
            return Result.Success(value: RemoveSubCategory(category: category
                                                           , subCategory: subCategory));
        }));
    }

    private CsvConfiguration CreateCsvConfiguration()
    {
        return new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };
    }

    private Dictionary<string, List<string>> CopyCategoryMappings()
    {
        return _categoryMappings.ToDictionary(keySelector: mapping => mapping.Key
                                              , elementSelector: mapping => new List<string>(collection: mapping.Value));
    }

    private List<string> GetSubCategoryList(string category)
    {
        return _categoryMappings.TryGetValue(key: category
                                             , value: out var subCategories)
            ? new List<string>(collection: subCategories)
            : new List<string>();
    }

    private bool IsValidCategoryAndSubCategory(string category
        , string subCategory)
    {
        return !string.IsNullOrWhiteSpace(value: category) && !string.IsNullOrWhiteSpace(value: subCategory);
    }

    private void AddOrUpdateSubCategory(string category
        , string subCategory)
    {
        if (_categoryMappings.TryGetValue(key: category
                                          , value: out var subCategories))
        {
            if (!subCategories.Contains(item: subCategory))
            {
                subCategories.Add(item: subCategory);
                FileHelper.AppendToFile(filePath: _filePath
                                        , category: category
                                        , subCategory: subCategory);
            }
        }
        else
        {
            _categoryMappings[key: category] = new List<string> { subCategory };
            FileHelper.AppendToFile(filePath: _filePath
                                    , category: category
                                    , subCategory: subCategory);
        }
    }

    private bool RemoveSubCategory(string category
        , string subCategory)
    {
        if (_categoryMappings.TryGetValue(key: category
                                          , value: out var subCategories) && subCategories.Remove(item: subCategory))
        {
            FileHelper.WriteAllMappingsToFile(filePath: _filePath
                                              , categoryMappings: _categoryMappings
                                              , csvConfig: _csvConfig);
            return true;
        }

        return false;
    }

    private static Result ExecuteSafely(Action function)
    {
        try
        {
            function.Invoke();
        }
        catch (Exception exception)
        {
            Console.Error.WriteLine(value: $"Error: {exception.Message}");
            return Result.Failure(error: "An error occurred while processing your request.");
        }

        return Result.Success();
    }

    private static Result<T> ExecuteSafely<T>(Func<Result<T>> function)
    {
        try
        {
            return function();
        }
        catch (Exception exception)
        {
            Console.Error.WriteLine(value: $"Error: {exception.Message}");
            return Result.Failure<T>(error: "An error occurred while processing your request.");
        }
    }

    private static class PathHelper
    {
        public static string PrependUserToAccountPath(string originalFilePath
            , string[] knownAccountNames)
        {
            try
            {
                var pathForProcessing = NormalizePathSeparators(path: originalFilePath);
                var pathSegments = new List<string>(collection: pathForProcessing.Split(separator: Path.DirectorySeparatorChar));

                var accountSegmentIndex = FindAccountSegmentIndex(pathSegments: pathSegments
                                                                  , knownAccountNames: knownAccountNames);

                if (accountSegmentIndex != -1)
                {
                    pathSegments.Insert(index: accountSegmentIndex
                                        , item: DefaultUser);
                    return string.Join(separator: Path.DirectorySeparatorChar.ToString()
                                       , values: pathSegments);
                }

                return originalFilePath;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(value: $"Error: {ex.Message}");
                return originalFilePath;
            }
        }

        private static string NormalizePathSeparators(string path)
        {
            return path.Replace(oldChar: Path.AltDirectorySeparatorChar
                                , newChar: Path.DirectorySeparatorChar)
               .Replace(oldValue: Path.DirectorySeparatorChar.ToString() + Path.DirectorySeparatorChar
                        , newValue: Path.DirectorySeparatorChar.ToString());
        }

        private static int FindAccountSegmentIndex(List<string> pathSegments
            , string[] knownAccountNames)
        {
            return pathSegments
               .Select(selector: (segment
                           , index) => new { segment, index })
               .Where(predicate: x => knownAccountNames.Any(predicate: knownAccount => x.segment.Equals(value: knownAccount
                                                                , comparisonType: StringComparison.OrdinalIgnoreCase)))
               .Select(selector: x => x.index > 0 && pathSegments[index: x.index - 1].Equals(value: DefaultUser
                           , comparisonType: StringComparison.OrdinalIgnoreCase)
                           ? -1
                           : x.index)
               .FirstOrDefault();
        }
    }

    private static class FileHelper
    {
        public static void EnsureFileExists(string filePath
            , CsvConfiguration csvConfig)
        {
            try
            {
                var directoryPath = Path.GetDirectoryName(path: filePath);
                if (!string.IsNullOrEmpty(value: directoryPath) && !Directory.Exists(path: directoryPath))
                    Directory.CreateDirectory(path: directoryPath);
                if (!File.Exists(path: filePath))
                    CreateCsvFile(filePath: filePath
                                  , csvConfig: csvConfig);
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(value: $"Error: {ex.Message}");
            }
        }

        private static void CreateCsvFile(string filePath
            , CsvConfiguration csvConfig)
        {
            using var writer = new StreamWriter(path: filePath
                                                , append: false);
            using var csvWriter = new CsvWriter(writer: writer
                                                , configuration: csvConfig);
            csvWriter.WriteHeader<CsvCategoryRecord>();
            csvWriter.NextRecord();
        }

        public static Dictionary<string, List<string>> LoadMappingsFromFile(string filePath
            , CsvConfiguration csvConfig)
        {
            try
            {
                using var reader = new StreamReader(path: filePath);
                using var csvReader = new CsvReader(reader: reader
                                                    , configuration: csvConfig);
                var records = csvReader.GetRecords<CsvCategoryRecord>();
                return GroupRecordsByCategory(records: records);
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(value: $"Error: {ex.Message}");
                return new Dictionary<string, List<string>>();
            }
        }

        private static Dictionary<string, List<string>> GroupRecordsByCategory(IEnumerable<CsvCategoryRecord> records)
        {
            return records
               .GroupBy(keySelector: record => record.Category)
               .ToDictionary(keySelector: group => group.Key
                             , elementSelector: group =>
                                 group.Select(selector: record => record.SubCategory).Distinct().ToList());
        }

        public static void AppendToFile(string filePath
            , string category
            , string subCategory)
        {
            try
            {
                using var writer = new StreamWriter(path: filePath
                                                    , append: true);
                using var csvWriter = new CsvWriter(writer: writer
                                                    , configuration: new CsvConfiguration(
                                                                         cultureInfo: CultureInfo.InvariantCulture)
                                                    {
                                                        HasHeaderRecord = false
                                                    });
                WriteCsvRecord(csvWriter: csvWriter
                               , category: category
                               , subCategory: subCategory);
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(value: $"Error: {ex.Message}");
            }
        }

        private static void WriteCsvRecord(CsvWriter csvWriter
            , string category
            , string subCategory)
        {
            csvWriter.WriteRecord(record: new CsvCategoryRecord { Category = category, SubCategory = subCategory });
            csvWriter.NextRecord();
        }

        public static void WriteAllMappingsToFile(string filePath
            , Dictionary<string, List<string>> categoryMappings
            , CsvConfiguration csvConfig)
        {
            try
            {
                using var writer = new StreamWriter(path: filePath
                                                    , append: false);
                using var csvWriter = new CsvWriter(writer: writer
                                                    , configuration: csvConfig);
                WriteAllRecords(csvWriter: csvWriter
                                , categoryMappings: categoryMappings);
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(value: $"Error: {ex.Message}");
            }
        }

        private static void WriteAllRecords(CsvWriter csvWriter
            , Dictionary<string, List<string>> categoryMappings)
        {
            csvWriter.WriteHeader<CsvCategoryRecord>();
            csvWriter.NextRecord();
            foreach (var category in categoryMappings.Keys)
            {
                foreach (var subCategory in categoryMappings[key: category])
                {
                    csvWriter.WriteRecord(record: new CsvCategoryRecord { Category = category, SubCategory = subCategory });
                    csvWriter.NextRecord();
                }
            }
        }
    }

    private class CsvCategoryRecord
    {
        public string Category { get; set; }
        public string SubCategory { get; set; }
    }
}