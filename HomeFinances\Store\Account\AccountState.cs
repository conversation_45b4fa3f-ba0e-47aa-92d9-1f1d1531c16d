using Fluxor;
using System;
using System.Collections.Generic;

namespace HomeFinances.Store.Account
{
    [FeatureState]
    public record AccountState
    {
        public Guid CurrentAccountId { get; init; }
        public IReadOnlyList<AccountInfo> AvailableAccounts { get; init; }

        // Private constructor for Fluxor
        private AccountState()
        {
            CurrentAccountId = Guid.Parse("********-0000-0000-0000-********0001"); // Default account
            AvailableAccounts = new List<AccountInfo>
            {
                new AccountInfo(Guid.Parse("********-0000-0000-0000-********0001"), "BeeHive"),
                new AccountInfo(Guid.Parse("********-0000-0000-0000-********0002"), "CitiBank"),
                new AccountInfo(Guid.Parse("********-0000-0000-0000-************"), "MountainAmerica")
            };
        }

        public AccountState(Guid currentAccountId, IReadOnlyList<AccountInfo> availableAccounts)
        {
            CurrentAccountId = currentAccountId;
            AvailableAccounts = availableAccounts ?? new List<AccountInfo>();
        }
    }

    public record AccountInfo(Guid Id, string Name);
}
