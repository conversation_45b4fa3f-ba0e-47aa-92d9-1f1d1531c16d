﻿using Azure.Storage.Blobs;
using HomeFinances.Models;
using System.Text.Json;

namespace HomeFinances.Services
{
    public class RecurringTransactionService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly string _blobContainerName = "data";
        private readonly string _blobName = "RecurringTransactions.json";

        public RecurringTransactionService(BlobServiceClient blobServiceClient)
        {
            _blobServiceClient = blobServiceClient;
        }

        public async Task<List<RecurringTransaction>> LoadTransactionsAsync()
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(_blobContainerName);
                var blobClient = containerClient.GetBlobClient(_blobName);

                if (await blobClient.ExistsAsync())
                {
                    var response = await blobClient.DownloadAsync();
                    using (var stream = response.Value.Content)
                    {
                        return await JsonSerializer.DeserializeAsync<List<RecurringTransaction>>(stream) ?? new List<RecurringTransaction>();
                    }
                }
                else
                {
                    return new List<RecurringTransaction>(); // Return empty list if blob doesn't exist
                }
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"JSON parsing error: {ex.Message}");
                return new List<RecurringTransaction>(); // Return an empty list on JSON error
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while reading the blob: {ex.Message}");
                throw; // Optionally re-throw the exception to handle it further up the call stack
            }
        }

        public async Task SaveTransactionsAsync(List<RecurringTransaction> transactions)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(_blobContainerName);
                var blobClient = containerClient.GetBlobClient(_blobName);

                using (var memoryStream = new MemoryStream())
                {
                    await JsonSerializer.SerializeAsync(memoryStream, transactions, new JsonSerializerOptions { WriteIndented = true });
                    memoryStream.Position = 0; // Reset stream position to beginning after writing
                    await blobClient.UploadAsync(memoryStream, overwrite: true);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while writing to the blob: {ex.Message}");
                throw; // Re-throw the exception to handle it further up the call stack
            }
        }

        public async Task AddOrUpdateTransaction(RecurringTransaction actionTransaction)
        {
            var transactions = await LoadTransactionsAsync();

            var existingTransactionIndex = transactions.FindIndex(t => t.Id == actionTransaction.Id);
            if (existingTransactionIndex != -1 && actionTransaction.Id != Guid.Empty)
            {
                transactions[existingTransactionIndex] = actionTransaction;
            }
            else
            {
                if (actionTransaction.Id == Guid.Empty)
                {
                    actionTransaction.Id = Guid.NewGuid();
                }
                transactions.Add(actionTransaction);
            }

            await SaveTransactionsAsync(transactions);
        }

        public async Task DeleteTransaction(Guid actionTransactionId)
        {
            var transactions = await LoadTransactionsAsync();
            transactions.RemoveAll(t => t.Id == actionTransactionId);
            await SaveTransactionsAsync(transactions);
        }
    }
}
