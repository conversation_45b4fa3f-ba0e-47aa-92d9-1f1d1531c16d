using System.Globalization;
using CSharpFunctionalExtensions;
using CsvHelper;
using CsvHelper.Configuration;
using HomeFinances.Models;
using HomeFinances.Models.Mapping;

namespace HomeFinances.Repositories;

/// <summary>
///     File-based implementation of IBankTransactionRepository using CSV files.
/// </summary>
public class FileBankTransactionRepository : IBankTransactionRepository
{
    private const string DefaultUser = "gordon";
    private static readonly string[] KnownAccountNames = { "BeeHive", "CitiBank", "MountainAmerica" };
    private readonly CsvConfiguration _csvConfig;

    public FileBankTransactionRepository()
    {
        _csvConfig = new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true
        };
    }

    public async Task<Result<List<BankTransaction>>> ReadTransactionsAsync(string filePath)
    {
        var actualFilePath = PrependUserToAccountPath(originalFilePath: filePath);
        var records = new List<BankTransaction>();
        if (!File.Exists(path: actualFilePath))
        {
            var directory = Path.GetDirectoryName(path: actualFilePath);
            if (!string.IsNullOrEmpty(value: directory) && !Directory.Exists(path: directory))
                Directory.CreateDirectory(path: directory);
            File.Create(path: actualFilePath).Close();
            return records;
        }

        try
        {
            using var reader = new StreamReader(path: actualFilePath);
            using var csv = new CsvReader(reader: reader
                                          , configuration: _csvConfig);
            csv.Context.RegisterClassMap<BankTransactionMap>();
            records = csv.GetRecords<BankTransaction>().ToList();
        }
        catch (Exception e)
        {
            return Result.Failure<List<BankTransaction>>(error: $"Error in reading Transactions {e.Message} {e.InnerException}");
        }

        return records;
    }

    public async Task<Result> WriteTransactionsAsync(IEnumerable<BankTransaction> transactions
        , string filePath)
    {
        var actualFilePath = PrependUserToAccountPath(originalFilePath: filePath);
        try
        {
            var directory = Path.GetDirectoryName(path: actualFilePath);
            if (!string.IsNullOrEmpty(value: directory) && !Directory.Exists(path: directory))
                Directory.CreateDirectory(path: directory);
            using var writer = new StreamWriter(path: actualFilePath
                                                , append: false);
            using var csv = new CsvWriter(writer: writer
                                          , configuration: _csvConfig);
            csv.Context.RegisterClassMap<BankTransactionMap>();
            csv.WriteRecords(records: transactions);
        }
        catch
        {
            // Logging can be added here
            return Result.Failure(error: "Error writing transactions.");
        }

        return Result.Success();
    }

    private string PrependUserToAccountPath(string originalFilePath)
    {
        // Normalize path separators for consistent processing within this method
        var pathForProcessing = originalFilePath.Replace(oldChar: Path.AltDirectorySeparatorChar
                                                         , newChar: Path.DirectorySeparatorChar);
        var segments = new List<string>(collection: pathForProcessing.Split(separator: Path.DirectorySeparatorChar));

        var accountSegmentIndex = -1;
        string matchedAccountName = null;

        for (var i = 0; i < segments.Count; i++)
        {
            var currentSegment = segments[index: i];
            foreach (var knownAccount in KnownAccountNames)
                if (currentSegment.Equals(value: knownAccount
                                          , comparisonType: StringComparison.OrdinalIgnoreCase))
                {
                    // Check if this account segment is already prefixed by DefaultUser
                    if (i > 0 && segments[index: i - 1].Equals(value: DefaultUser
                                                               , comparisonType: StringComparison.OrdinalIgnoreCase))
                        return originalFilePath; // Path is already in user/account format
                    accountSegmentIndex = i;
                    matchedAccountName = currentSegment; // Keep original casing for potential reconstruction
                    goto found_account_segment; // Exit both loops
                }
        }

    found_account_segment:
        if (accountSegmentIndex != -1)
        {
            segments.Insert(index: accountSegmentIndex
                            , item: DefaultUser);
            // Reconstruct the path using the system's primary directory separator.
            // This is generally safe as .NET file APIs handle mixed separators on Windows.
            return string.Join(separator: Path.DirectorySeparatorChar.ToString()
                               , values: segments);
        }

        return originalFilePath; // No known account found, or structure doesn't match expectations
    }
}