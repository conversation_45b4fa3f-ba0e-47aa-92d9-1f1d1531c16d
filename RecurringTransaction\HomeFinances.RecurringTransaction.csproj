﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.20.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HomeFinances\HomeFinances.csproj" />
  </ItemGroup>
  <!-- Temporarily commented out to break circular reference -->
  <!--
  <ItemGroup>
    <ProjectReference Include="..\HomeFinances\HomeFinances.csproj" />
  </ItemGroup>
  -->

</Project>
