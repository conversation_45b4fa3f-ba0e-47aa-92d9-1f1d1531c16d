﻿using Fluxor;
using HomeFinances.Models;
using static HomeFinances.Store.BankTransactions.BankTransactionActions;

namespace HomeFinances.Store.BankTransactions
{
    public static class BankTransactionReducers
    {
        [ReducerMethod]
        public static BankTransactionState Reduce(BankTransactionState state, LoadTransactionsAction action)
        {
            var newState = state with { Loading = new LoadingState { Status = LoadingStatusEnum.Loading } };
            return newState;
        }

        [ReducerMethod]
        public static BankTransactionState Reduce(BankTransactionState state, LoadTransactionsSuccessAction successAction)
        {
            var newState = state with { Transactions = successAction.Transactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };
            return newState;
        }
        [ReducerMethod]
        public static BankTransactionState Reduce(BankTransactionState state, LoadTransactionsFailedAction action)
        {
            var newState = state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = action.ErrorMessage } };
            return newState;
        }

        [ReducerMethod]
        public static BankTransactionState ReduceMergeBeehiveTransactionsAction(BankTransactionState state, MergeBeehiveTransactionsAction action)
        {
            return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Loading } };
        }

        [ReducerMethod]
        public static BankTransactionState ReduceMergeBeehiveTransactionsSuccessAction(BankTransactionState state, MergeBeehiveTransactionsSuccessAction action)
        {
            var updatedTransactions = state.Transactions.Concat(action.BankTransactions).ToList();
            return state with { Transactions = updatedTransactions, Loading = new LoadingState { Status = LoadingStatusEnum.Loaded } };
        }

        [ReducerMethod]
        public static BankTransactionState ReduceMergeBeehiveTransactionsFailedAction(BankTransactionState state, MergeBeehiveTransactionsFailedAction action)
        {
            return state with { Loading = new LoadingState { Status = LoadingStatusEnum.Error, ErrorMessage = action.ErrorMessage } };
        }


    }
}