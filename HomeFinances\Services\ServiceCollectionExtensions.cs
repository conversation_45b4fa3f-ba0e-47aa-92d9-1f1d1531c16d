﻿using System.Text.Json;
using Amazon;
using Amazon.DynamoDBv2;
using Amazon.Runtime;
using Amazon.S3;
using Azure.Core;
using Azure.Data.Tables;
using Azure.Identity;
using Azure.Storage.Blobs;
using Fluxor;
using HomeFinances.Repositories;
using HomeFinances.Store.Account;
using HomeFinances.Store.FutureTransactions;

// using HomeFinances.Store.Account; // Removed to avoid duplicate registration

namespace HomeFinances.Services;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAppServices(this IServiceCollection services)
    {
        // Register Azure clients as singletons
        services.AddSingleton<BlobServiceClient>(implementationFactory: serviceProvider =>
        {
            var logger = serviceProvider.GetRequiredService<ILogger<BlobServiceClient>>();
            var blobServiceClientOptions = new BlobClientOptions
            {
                Retry =
                                               {
                                                   Mode = RetryMode.Exponential, MaxRetries = 3
                                                   , Delay = TimeSpan.FromSeconds(value: 2)
                                                   , MaxDelay = TimeSpan.FromSeconds(value: 32)
                                               }
                                               ,
                Diagnostics =
                                               {
                                                   IsLoggingEnabled = true, IsDistributedTracingEnabled = true
                                                   , ApplicationId = "HomeFinances"
                                               }
            };

            var storageAccountUrl = "https://homefinances.blob.core.windows.net/";
            logger.LogInformation(message: "Initializing BlobServiceClient with URL: {Url}"
                                  , storageAccountUrl);
            var client = new BlobServiceClient(serviceUri: new Uri(uriString: storageAccountUrl)
                                               , credential: new DefaultAzureCredential()
                                               , options: blobServiceClientOptions);
            logger.LogInformation(message: "BlobServiceClient initialized successfully");
            return client;
        });

        services.AddSingleton<TableServiceClient>(implementationFactory: serviceProvider =>
        {
            var logger = serviceProvider.GetRequiredService<ILogger<TableServiceClient>>();
            var tableServiceClientOptions = new TableClientOptions
            {
                Retry =
                                                {
                                                    Mode = RetryMode.Exponential, MaxRetries = 3
                                                    , Delay = TimeSpan.FromSeconds(value: 2)
                                                    , MaxDelay = TimeSpan.FromSeconds(value: 32)
                                                }
                                                ,
                Diagnostics =
                                                {
                                                    IsLoggingEnabled = true, IsDistributedTracingEnabled = true
                                                    , ApplicationId = "HomeFinances"
                                                }
            };

            var tableAccountUrl = "https://homefinances.table.core.windows.net/";
            logger.LogInformation(message: "Initializing TableServiceClient with URL: {Url}"
                                  , tableAccountUrl);
            var client = new TableServiceClient(endpoint: new Uri(uriString: tableAccountUrl)
                                                , tokenCredential: new DefaultAzureCredential()
                                                , options: tableServiceClientOptions);
            logger.LogInformation(message: "TableServiceClient initialized successfully");
            return client;
        });

        // Add HttpClient
        services.AddHttpClient<ChatGptApiService>(configureClient: (serviceProvider
                                                      , client) =>
                                                  {
                                                      var configuration = serviceProvider.GetRequiredService<IConfiguration>();
                                                      var apiKey = Environment.GetEnvironmentVariable(variable: "OPENAI_API_KEY");
                                                      if (string.IsNullOrEmpty(value: apiKey))
                                                          throw new InvalidOperationException(
                                                              message:
                                                              "OpenAI API key not found in environment variables. Please set OPENAI_API_KEY.");
                                                      client.DefaultRequestHeaders.Add(name: "Authorization"
                                                          , value: $"Bearer {apiKey}");
                                                  });

        // Get AWS credentials from environment variable
        Dictionary<string, string> awsConnectionSettings = null;
        string accessKey = null;
        string secretKey = null;
        string regionName = null;
        string bucketName = null;
        var useAwsBackend = false;

        try
        {
            var connectionJson = Environment.GetEnvironmentVariable(variable: "AWS_S3_HOMEFINANCE_CONNECTION");
            if (!string.IsNullOrEmpty(value: connectionJson))
            {
                awsConnectionSettings = JsonSerializer.Deserialize<Dictionary<string, string>>(json: connectionJson);
                if (awsConnectionSettings != null)
                {
                    awsConnectionSettings.TryGetValue(key: "AccessKey"
                                                      , value: out accessKey);
                    awsConnectionSettings.TryGetValue(key: "SecretKey"
                                                      , value: out secretKey);
                    awsConnectionSettings.TryGetValue(key: "Region"
                                                      , value: out regionName);
                    awsConnectionSettings.TryGetValue(key: "BucketName"
                                                      , value: out bucketName);

                    useAwsBackend = !string.IsNullOrEmpty(value: accessKey) &&
                                    !string.IsNullOrEmpty(value: secretKey) &&
                                    !string.IsNullOrEmpty(value: regionName) &&
                                    !string.IsNullOrEmpty(value: bucketName);
                }
            }
        }
        catch (Exception ex)
        {
            var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Program>>();
            logger.LogError(exception: ex
                            , message: "Error parsing AWS connection settings");
        }

        // Register AWS DynamoDB client
        services.AddSingleton<IAmazonDynamoDB>(implementationFactory: serviceProvider =>
        {
            var logger = serviceProvider.GetRequiredService<ILogger<IAmazonDynamoDB>>();

            if (!useAwsBackend)
            {
                logger.LogWarning(message: "AWS credentials not properly configured. Using file-based repository instead.");
                return null;
            }

            try
            {
                // Create AWS credentials and client
                RegionEndpoint region;
                try
                {
                    region = RegionEndpoint.GetBySystemName(systemName: regionName);
                    if (region == null)
                    {
                        logger.LogWarning(message: "Invalid AWS region name: {RegionName}. Using file-based repository instead."
                                          , regionName);
                        return null;
                    }
                }
                catch (Exception regionEx)
                {
                    logger.LogError(exception: regionEx
                                    , message:
                                    "Error getting AWS region endpoint for {RegionName}. Using file-based repository instead."
                                    , regionName);
                    return null;
                }

                try
                {
                    var credentials = new BasicAWSCredentials(accessKey: accessKey
                                                              , secretKey: secretKey);
                    var clientConfig = new AmazonDynamoDBConfig
                    {
                        RegionEndpoint = region,
                        Timeout = TimeSpan.FromSeconds(value: 10),
                        MaxErrorRetry = 3
                    };

                    logger.LogInformation(message: "Successfully configured AWS DynamoDB client with region: {Region}"
                                          , regionName);
                    return new AmazonDynamoDBClient(credentials: credentials
                                                    , clientConfig: clientConfig);
                }
                catch (Exception clientEx)
                {
                    logger.LogError(exception: clientEx
                                    , message: "Error creating AmazonDynamoDBClient. Using file-based repository instead.");
                    return null;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(exception: ex
                                , message:
                                "Unexpected error configuring AWS DynamoDB client. Using file-based repository instead.");
                return null;
            }
        });

        // Register AWS S3 client
        services.AddSingleton<IAmazonS3>(implementationFactory: serviceProvider =>
        {
            var logger = serviceProvider.GetRequiredService<ILogger<IAmazonS3>>();

            if (!useAwsBackend)
            {
                logger.LogWarning(message: "AWS credentials not properly configured. Using file-based repository instead.");
                return null;
            }

            try
            {
                // Create AWS credentials and client
                RegionEndpoint region;
                try
                {
                    region = RegionEndpoint.GetBySystemName(systemName: regionName);
                    if (region == null)
                    {
                        logger.LogWarning(message: "Invalid AWS region name: {RegionName}. Using file-based repository instead."
                                          , regionName);
                        return null;
                    }
                }
                catch (Exception regionEx)
                {
                    logger.LogError(exception: regionEx
                                    , message:
                                    "Error getting AWS region endpoint for {RegionName}. Using file-based repository instead."
                                    , regionName);
                    return null;
                }

                try
                {
                    var credentials = new BasicAWSCredentials(accessKey: accessKey
                                                              , secretKey: secretKey);
                    var clientConfig = new AmazonS3Config
                    {
                        RegionEndpoint = region,
                        Timeout = TimeSpan.FromSeconds(value: 10),
                        MaxErrorRetry = 3
                    };

                    logger.LogInformation(message: "Successfully configured AWS S3 client with region: {Region}"
                                          , regionName);
                    return new AmazonS3Client(credentials: credentials
                                              , clientConfig: clientConfig);
                }
                catch (Exception clientEx)
                {
                    logger.LogError(exception: clientEx
                                    , message: "Error creating AmazonS3Client. Using file-based repository instead.");
                    return null;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(exception: ex
                                , message: "Unexpected error configuring AWS S3 client. Using file-based repository instead.");
                return null;
            }
        });

        // Register repositories
        services.AddScoped<ICategoryRepository>(implementationFactory: serviceProvider =>
        {
            var accountStateAccessor = serviceProvider.GetRequiredService<IState<AccountState>>();
            return new FileCategoryRepository(accountStateAccessor: accountStateAccessor);
        });

        services.AddScoped<ICategoryMappingRepository>(implementationFactory: serviceProvider =>
        {
            var accountStateAccessor = serviceProvider.GetRequiredService<IState<AccountState>>();
            return new FileCategoryMappingRepository(accountStateAccessor: accountStateAccessor);
        });

        // For this refactoring, we are focusing on the file-based transaction repository.
        // The original AWS S3 setup can be reinstated or made conditional later.
        services.AddScoped<IBankTransactionRepository, FileBankTransactionRepository>();

        // if (useAwsBackend) {
        //     services.AddScoped<IBankTransactionRepository, AwsS3BankTransactionRepository>(); // Or your factory
        // } else {
        //     services.AddScoped<IBankTransactionRepository, FileBankTransactionRepository>();
        // }

        // Register FutureTransaction and RecurringTransaction repositories
        // Temporarily commented out to break circular reference
        // services.AddScoped<HomeFinances.FutureTransaction.Core.IFutureTransactionRepository, HomeFinances.FutureTransaction.Infrastructure.FutureTransactionRepository>();
        // services.AddScoped<HomeFinances.RecurringTransaction.Core.IRecurringTransactionRepository, HomeFinances.RecurringTransaction.Infrastructure.RecurringTransactionRepository>();

        // Register services as scoped
        services.AddScoped<ImageStorageService>();
        services.AddScoped<ImageGenerationRepository>();
        services.AddScoped<CategoryMappingService>();
        services.AddScoped<CategoryService>();
        services.AddScoped<OpenAiCategoryService>();
        services.AddScoped<TransactionReadWriteService>();
        services.AddScoped<FutureTransactionService>();
        services.AddScoped<CategorySpendingComparisonService>();
        services.AddScoped<RecurringTransactionService>();
        services.AddScoped<IgnoredTransactionsService>();
        services.AddScoped<DataArchiveService>();
        services.AddScoped<FileService>();
        services.AddScoped<FutureTransactionEffects>();

        return services;
    }
}